/**
 * 不同对象级别授权
 */
import React, { useEffect, useState } from 'react'
import { Input, Alert, Table, Spin, Button, Dropdown, Menu, InputNumber, message } from "antd"
import * as _ from 'lodash'
import { useTranslation } from 'react-i18next';
import {
  queryDmsNodeData,
  queryTableGroupData,
} from "src/api";
import { SearchOutlined, UserOutlined } from "@ant-design/icons"
import type { ColumnsType } from "antd/es/table"
import type { TableRowSelection } from "antd/es/table/interface"
import NewAuthorizeDrawer from './newAuthorizeDrawer'
import styles from "./index.module.scss";
import classnames from 'classnames'
import { useRequest, useSelector } from 'src/hook';
import { renderTableFields } from 'src/util'
import { Iconfont } from 'src/components';
import PermissionTooltip from 'src/components/PermissionTooltip/PermissionTooltip';

interface IProps { 
	[p:string]:any
}
const ObjectLevelAuthorization = (props: IProps) => {
  const { selectTreeItem, selectNodeType } = props;

  const { t } = useTranslation();
  const [filterNodeName, setFilterNodeName] = useState<string>('')
  const [activeKey, setActiveKey] = useState<string>("1");
  const [drawerVisible, setDrawerVisible] = useState(false);
  const [selectedRowKeys, setSelectedRowKeys] = useState<any[]>([]);
  const [selectedRows, setSelectedRows] = useState<any[]>([]);
  const [selectItems, setSelectItems] = useState<any[]>([]);
  const [pagination, setPagination] = useState({
    current: 1,
    pageSize: 10,
    total: 1,
  });
  const [curNodeDatas, setCurNodeDatas] = useState<any>([]);
  const [curPagination, setCurPagination] = useState<{pageNo: number, pageSize: number}>({
    pageNo: 1,
    pageSize: 10
  })
  const [showDefaultPagination, setShowDefaultPagination] = useState(true)
  const [data, setData] = useState<any[]>([])
  // 判断 该登录权限下,该模块 只读/可编辑
  const { permissionList } = useSelector((state) => state?.login)
  const [permissionlist, setPermissionlist] = useState<{isOnlyRead: boolean, roleNameList: any}>()

  const { data: nodeDatas, run: queryNodes, loading: nodeLoading } = useRequest(
    queryDmsNodeData,
    {
      manual: true,
      onSuccess: (res) => {
        setCurNodeDatas(res || []);
      },
    }
  );

  // 列表数据查询
  const { run: getTablesData, refresh: refreshTableList, loading } = useRequest(queryTableGroupData, {
    manual: true,
    onSuccess: (res) => {
      setData(res || [])
    }
  });

  useEffect(() => {
    const pathName = (window.location.pathname.split("/")[1].toUpperCase() as keyof typeof permissionList)
    setPermissionlist(permissionList?.DATABASE_MANAGEMENT?.[pathName])
  }, [])

  useEffect(() => {
    setShowDefaultPagination(!selectTreeItem?.sdt?.supportPaging)
    setData([])
  }, [selectTreeItem]);

  useEffect(() => {
    queryDatas()
  }, [queryNodes, getTablesData, selectTreeItem]);

  const queryDatas = (paramsent?: any) => {
    if (!selectTreeItem) {
      return;
    }
    const {
      nodeName,
      nodePath,
      nodePathWithType,
      nodeType,
      sdt: { connectionId, connectionType, supportPaging },
    } = selectTreeItem;
    let params: any = {
      connectionId,
      connectionType,
      nodeType,
      nodeName,
      nodePath,
      nodePathWithType,
    };
    if(supportPaging){
      // 接口支持分页
      params = { 
        pageNo:1,
        pageSize: 10,
        ...params,
        ...paramsent 
      };
      setCurPagination({pageNo: params?.pageNo, pageSize: params?.pageSize})
      queryNodes(params)
        .then((res: any) => {
          getTablesData(res)
        })
        .catch((err: any) => {
          console.log("查询所有元素失败:", err);
        });
    }else {
      // 前端分页(初始化时先获取nodes参数信息,后续直接请求getTablesData)
      queryNodes(params)
        .then((res: any) => {
          setPagination({
            current: 1,
            pageSize: 10,
            total: res?.length,
          });
          const paraments = res?.slice(0, 10);
          getTablesData(paraments);
        })
        .catch((err: any) => {
          console.log("查询所有元素失败:", err);
        });
    }
  }

  const rowSelection: TableRowSelection<any> = {
    selectedRowKeys: selectedRowKeys,
    onSelectAll(selected, newSelectedRows) {
      const curRowKeys = newSelectedRows.map(row => row?.nodePath);
      let cloneSelectedRowKeys = _.cloneDeep(selectedRowKeys);
      let cloneSelectedRows = _.cloneDeep(selectedRows);
      if (selected) {
        cloneSelectedRows = cloneSelectedRows.concat(newSelectedRows)
        cloneSelectedRowKeys = cloneSelectedRowKeys.concat(curRowKeys)
      }else {
        const curKeys = data.map(row => row?.nodePath);
        cloneSelectedRows = cloneSelectedRows.filter(cr => cr && !curKeys.includes(cr?.nodePath));
        cloneSelectedRowKeys = cloneSelectedRowKeys.filter(k => !curKeys.includes(k))
      }
      setSelectedRowKeys(cloneSelectedRowKeys.filter(i => !_.isEmpty(i)));
      setSelectedRows(cloneSelectedRows.filter(i => !_.isEmpty(i)));
    },
    onSelect(item, selected) {
      let cloneSelectedRowKeys = _.cloneDeep(selectedRowKeys);
      let cloneSelectedRows = _.cloneDeep(selectedRows);
      if (selected) {
        cloneSelectedRows = cloneSelectedRows.concat([item])
        cloneSelectedRowKeys = cloneSelectedRowKeys.concat([item.nodePath])
      }else {
        cloneSelectedRows = cloneSelectedRows.filter(cr => cr?.nodePath !== item.nodePath );
        cloneSelectedRowKeys = cloneSelectedRowKeys.filter(k => k !== item.nodePath)
      }
      setSelectedRowKeys(cloneSelectedRowKeys);
      setSelectedRows(cloneSelectedRows);
    },
  };

  // 批量授权、安全设置
  const handleBatchSetting = (e: any) => {
    setActiveKey(e.key);
    setDrawerVisible(true);
    setSelectItems(selectedRows);
  };

  // 关闭抽屉并刷新数据
  const handleClose = () => {
    setDrawerVisible(false);
    setSelectedRowKeys([]);
    setSelectedRows([])
    setSelectItems([]);
    refreshTableList();
  };

  // 页码修改
  const handlePageChange = (page: number, pageSize: number = 10) => {
    const start = pageSize * (page - 1) || 0;
    const end = pageSize * page;
    const curNodeDatasTmp = curNodeDatas?.filter((i: any) => i?.nodeName?.toLowerCase()?.includes(filterNodeName?.toLowerCase())) || [];
    const paraments = curNodeDatasTmp?.slice(start, end);
    setCurNodeDatas(curNodeDatasTmp);
    getTablesData(paraments);
    setPagination((p) => {
      return { ...p, current: page, pageSize };
    });
  };

  // 搜索
  const handleSearch = (e: any) => {
    const searchValue = e.target.value?.trim()
    setFilterNodeName(searchValue)
    // 接口搜索
    if(!showDefaultPagination){
      queryDatas({filterNodeName: searchValue})
      return
    }
    // 前端搜索
    const value = searchValue?.toLowerCase()
    if (value) {
      // 当前所显示的记录为搜索基数，在该基数上进行筛选
      const resultTmp = _.cloneDeep(curNodeDatas || []);
      const result = resultTmp.filter((i: any) =>
        i?.objectName?.toLowerCase()?.includes(value)
      );
      setPagination((p: any)=>{
        return {
          ...p, 
          current: 1,
          total: result?.length
        }
      })
      const paraments = result?.slice(0, pagination?.pageSize);
      getTablesData(paraments);
    } else { 
      setPagination({
        current: 1,
        pageSize: pagination?.pageSize,
        total: nodeDatas?.length,
      });
      setCurNodeDatas(nodeDatas);
      const paraments = nodeDatas?.slice(0, 10);
      getTablesData(paraments);
    }
  }

  // 操作
  const clickOperation = (key: string, record: any) => {
    setActiveKey(key);
    setDrawerVisible(true);
    setSelectItems([record]);
  }

  const columns: ColumnsType<any> = [
    {
      title: selectTreeItem?.nodeName===t('common.dictSearch.nodeName.table') ? t('db.auth.connectSetting.object.tableName') : selectTreeItem?.nodeName,
      dataIndex: "objectName",
      key: "objectName",
      render: (txt: string, record: any) => (
        <>
          <Iconfont type={`icon-table`} />
          <span className={styles.ml4}>{renderTableFields(txt)}</span>
        </>
      ),
    },
    {
      title: t('db.auth.connectSetting.object.authUpdateMsg'),
      dataIndex: "authUpdateMsg",
      key: "authUpdateMsg",
      render:(txt: string)=><span>{renderTableFields(txt)}</span>
    },
    {
      title: t('db.auth.connectSetting.object.authUserCnt'),
      dataIndex: "authUserCnt",
      key: "authUserCnt",
      width: 100,
      render: (txt: string, record: any) => (
        <span>
          <UserOutlined style={{marginRight: 2}} />
          {renderTableFields(txt)}
        </span>
      ),
    },
    {
      title: t('common.text.action'),
      dataIndex: 'operation',
      width: 100,
      fixed: 'right',
      render: (_: any, record: any) => {
        return (
          <Button type="link" className={styles.pl0} onClick={()=>clickOperation("1", record)}>{t('db.auth.connectSetting.tab.users')}</Button>
        )
      }
    },
  ];

  if (selectTreeItem?.nodeType === "synonymsGroup") {
    columns.splice(1, 0, {
      title: t('db.auth.connectSetting.object.synonymType'),
      dataIndex: "synonymType",
      key: "synonymType",
      width: 160,
      render:(txt: string)=><span>{renderTableFields(txt)}</span>
    });
  }

  const handlePageNumChange = (pageNo: number|string|undefined) => {
    if(!pageNo){
      return
    }
    // 解决输入页码查询不失去焦点又立即去点击上下页页码，数据重复请求问题
    document.getElementById("pageInputNumber")?.blur();
    queryDatas({pageNo, filterNodeName})
  }

  const handlePageNumUpdate = _.debounce((pageNo: number|string|undefined) => {
    const reg = /^[1-9]\d*$/;
    if (!reg.test(pageNo + '')) {
      return message.warning(t('db.auth.connectSetting.object.pageNum.hint'));
    }
    handlePageNumChange(pageNo)
  }, 500)

  const paginationConfig = showDefaultPagination ?
    {
      ...pagination,
      onChange: handlePageChange,
    }
    : false

  return (
    <Spin spinning={nodeLoading || loading}>
      <div className={styles.tableWrap}>
        <div className={styles.header}>
          <span className={styles.title}>
            {selectTreeItem?.nodeName + t('db.auth.connectSetting.object.object')}
          </span>
          <Input
            allowClear
            style={{ width: "200px" }}
            prefix={<SearchOutlined />}
            placeholder={t('db.auth.connectSetting.object.search', {val: selectTreeItem?.nodeName})}
            onChange={handleSearch}
          />
        </div>
        <Alert
          className={styles.mb20}
          message={
            <span>
              {t('db.auth.connectSetting.object.alert1')}
              <span className={styles.color3357ff}>
                {t('db.auth.connectSetting.object.alert2')}{selectTreeItem?.nodeName}
              </span>
              {t('db.auth.connectSetting.object.alert3')}
              <span className={styles.color3357ff}>{t('common.btn.batchSetting')}</span>
            </span>
          }
          type="info"
          showIcon
          closable
        />
        {(!!data?.length || !showDefaultPagination) && (
          <>
            {
              permissionlist && 
              <PermissionTooltip
                title={t('db.auth.modal.title')}
                permissionlist={permissionlist}
                align={{points: ['bl', 'tl'] ,offset: [0, 4]}}
              >
                <Dropdown
                  overlay={
                    !selectedRowKeys?.length || permissionlist?.isOnlyRead ?
                    <></>
                    : <Menu
                        className={styles.options}
                        style={{ width:  "182px" }}
                        onClick={(e) => selectedRowKeys?.length && handleBatchSetting(e)}
                      >
                        <Menu.Item key="1">{t('db.auth.connectSetting.tab.users')}</Menu.Item>
                      </Menu>
                  }
                >
                  <div
                    className={classnames(styles.mb10, {
                      [styles.options]: selectedRowKeys?.length,
                      [styles.disabled]: !selectedRowKeys?.length || permissionlist?.isOnlyRead,
                    })}
                  >
                    {t('common.btn.batchSetting')}
                  </div>
                </Dropdown>
              </PermissionTooltip>
            }
            <Table
              rowSelection={rowSelection}
              rowKey={(item) => item.nodePath}
              columns={columns}
              dataSource={data}
              pagination={paginationConfig}
              scroll={{
                x: "max-content",
                y: "calc(100vh - 440px)",
              }}
            />
            {
              // schema支持分页,有超过一页的数据
              !showDefaultPagination &&
              !(curPagination?.pageNo === 1 && !data?.length) &&
              !(curPagination?.pageNo === 1 && (curPagination?.pageSize !== data?.length)) &&
              <div className='flexAlignCenterJustifyEnd mtb10'>
                <span className='mr10'>{t('db.auth.connectSetting.object.pageNum', {num: curPagination?.pageNo})}</span>
                <Button 
                  onClick={()=>handlePageNumChange(curPagination?.pageNo - 1)} 
                  disabled={curPagination?.pageNo === 1}
                  className='mr10 options'
                  size='small'
                >
                  {t('common.btn.prePage')}
                </Button>  
                <Button 
                  onClick={()=>handlePageNumChange(curPagination?.pageNo + 1)} 
                  disabled={curPagination?.pageSize !== data?.length}
                  className='mr10 options'
                  size='small'
                >
                  {t('common.btn.nextPage')}
                </Button>
                {t('db.auth.connectSetting.object.pageTo')}
                <InputNumber 
                  id="pageInputNumber"
                  size='small'
                  className='ml10 w60'
                  min={1} 
                  onChange={handlePageNumUpdate}
                />
              </div>
            }
          </>
        )}
        {/* 权限设置 */}
        {drawerVisible && (
          <NewAuthorizeDrawer
            visible
            handleClose={handleClose}
            selectItems={selectItems}
            initActiveKey={activeKey}
            permissionlist={permissionlist}
            selectNodeType={selectNodeType}
          />
        )}
      </div>
    </Spin>
  );
};
export default ObjectLevelAuthorization;
