import { IconFontProps } from '@ant-design/icons/lib/components/IconFont'
import { createSlice, PayloadAction } from '@reduxjs/toolkit'
import { DEFAULT_KEY } from './constant'
import { ElementNodeProps } from './SdtTree/util'

export interface TreeNode {
  key: string
  value: string
  title: string
  props: ElementNodeProps & { id: number | null }
  icon?: React.FC<IconFontProps<string>>
  isLeaf: boolean
  nodeType: string
  nodeName: string
  id: string | number
  nodePathWithType: string | null
  nodePath: string
  sdt?: any
  connection: {
    connectionType: string
  },
  connectionId?: string | number |null;
  connectionType?: string
  newNodeType?: string;
}

interface InitialState {
  selectedNode: TreeNode | null
  scanCntTreeData:any[] // 当前所选节点所在的连接树，【用于脱敏扫描设置扫描范围】
  scanKeyList:string[] // 当前所选节点的展开路径key 列表，【用于脱敏扫描设置扫描范围定位到当前所选节点】
  activeTab: string
  columnType: string
  nodePathOfColumn: string
  importDesConfigModalVisible: boolean,
  desensRuleSettingObj: any;
  desensPolicySettingObj: any;
  desensitizedUserListObj: any;
  selectedNodePermissionInfo: {
    [key: string]: {
      modulePermissionObj: {
        isOnlyRead: boolean, 
        roleNameList: string[],
      };
      permissionSupportData: boolean;
    };
  };
  securitySettingSubActiveKey: string;
}

const initialState: InitialState = {
  selectedNode: null,
  scanCntTreeData:[],
  scanKeyList:[],
  activeTab: DEFAULT_KEY,
  columnType: '',
  nodePathOfColumn: '',
  importDesConfigModalVisible: false,
  desensRuleSettingObj: {}, //编辑脱敏字段存储信息
  desensPolicySettingObj: {}, //脱敏字段 策略设置
  desensitizedUserListObj: {}, // 脱敏用户列表 设置
  selectedNodePermissionInfo: {}, // 数据保护，权限禁用前置 
  securitySettingSubActiveKey: 'basicSetting',//安全设置当前tab
}

export const dataProtectionSlice = createSlice({
  name: 'dataProtection',
  initialState,
  reducers: {
    setSelectedNode: (state, action: PayloadAction<TreeNode>) => {
      state.selectedNode = action.payload
    },
    setScanCntTreeData: (state, action: PayloadAction<any[]>) => {
      state.scanCntTreeData = action.payload
    },
    setScanKeyList: (state, action: PayloadAction<string[]>) => {
      state.scanKeyList = action.payload
    },
    setActiveTab: (state, action: PayloadAction<string>) => {
      state.activeTab = action.payload
    },
    setColumnType: (state, action: PayloadAction<string>) => {
      state.columnType = action.payload
    },
    setNodePathOfColumn: (state, action: PayloadAction<string>) => {
      state.nodePathOfColumn = action.payload
    },
    setImportDesConfigModalVisible:  (state, action: PayloadAction<boolean>) => {
      state.importDesConfigModalVisible = action.payload
    },
    setDesensRuleSettingObj:(state, action: PayloadAction<any>) => {
      state.desensRuleSettingObj = action.payload
    },
    resetDesensRuleSettingObj:(state) => {
      state.desensRuleSettingObj = {}
    },
    //敏感用户列表
    setDesensitizedUserListObj:(state, action: PayloadAction<any>) => {
      state.desensitizedUserListObj = action.payload
    },
    resetDesensitizedUserListObj:(state) => {
      state.desensitizedUserListObj = {}
    },
    setDesensPolicySettingObj:(state, action: PayloadAction<any>) => {
      state.desensPolicySettingObj = action.payload
    },
    resetDesensPolicySettingObj:(state) => {
      state.desensPolicySettingObj = {}
    },
    setSelectedNodePermissionInfo: (state, action: PayloadAction<any>) => {
      state.selectedNodePermissionInfo = action.payload
    },
    setSecuritySettingSubActiveKey: (state, action: PayloadAction<string>) => {
      state.securitySettingSubActiveKey = action.payload
    },
  },
})

export const dataProtectionReducer = dataProtectionSlice.reducer

export const {
  setSelectedNode,
  setScanCntTreeData,
  setScanKeyList,
  setActiveTab,
  setColumnType,
  setNodePathOfColumn,
  setImportDesConfigModalVisible,
  setDesensRuleSettingObj,
  resetDesensRuleSettingObj,
  setDesensPolicySettingObj,
  resetDesensPolicySettingObj,
  setDesensitizedUserListObj,
  resetDesensitizedUserListObj,
  setSelectedNodePermissionInfo,
  setSecuritySettingSubActiveKey
} = dataProtectionSlice.actions
