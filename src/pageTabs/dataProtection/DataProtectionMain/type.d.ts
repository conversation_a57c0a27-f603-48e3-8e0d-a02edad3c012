import { TABS_PANE } from "../constant";

type TabKey = keyof typeof TABS_PANE;
type NodeTypeKey =
  'datasource'
  | 'group'
  | 'root'
  | 'connectionGroup'
  | 'connection'
  | 'catalog'
  | 'database'
  | 'schema'
  | 'table'
  | 'view'
  | 'column'
  | 'tableGroup'
  | 'viewGroup'
  | 'functionGroup'
  | 'udfGroup'
  | 'udf'
  | 'storedProcedureGroup'
  | 'favoriteGroup'
  | 'columnGroup'
  | 'oracleUser'
  | 'redisDataBase'
  | 'redisKey'
  | 'file'
  | 'collection'
  | 'function'
  | 'procedure'
  | 'synonym'
  | 'sequence'
  | 'package'
  | 'type'
  | 'packageBody'
  | 'materializedView'
  | 'trigger'
  | 'triggerGroup'
  | 'foreignTable'
  | 'syncMaterializedView'
  | 'asyncMaterializedView'
  | 'other'
  | 'materializedViewGroup'
  | 'syncMaterializedViewGroup'
  | 'asyncMaterializedViewGroup'
  | 'routineLoad'
  | 'routineLoadGroup'
  | 'procedureGroup'
  | 'job'
  | 'jobGroup'
  | 'index' | 'indexGroup'
  | 'constraint' | 'constraintGroup'
  | 'packageGroup'
  | 'packageBodyGroup'
  | 'synonymGroup'
  | 'dbLink'
  | 'dbLinkGroup'
  | 'foreignTableGroup'
  | 'dictionary'
  | 'dictionaryGroup'
  | 'collectionGroup'
  | 'redisKeyGroup'
  | 'fileGroup'
  | 'foreignKey'
  | 'foreignKeyGroup'
  | 'flexTable'
  | 'flexTableGroup'
  | 'part'
  | 'partGroup'
  | 'GridFs'
  | 'GridFsGroup'
  | 'sequenceGroup'
  | 'sequencesGroup'
  | 'synonyms'
  | 'jobs'
  | 'jobsGroup'
  | 'synonymsGroup'
  | 'typeGroup'

type ConfigKey = `${NodeTypeKey}_${TabKey}`;

type Config = {
  [T in ConfigKey]?: JSX.Element;
};

export type { ConfigKey, Config, TabKey, NodeTypeKey };

