import { <PERSON><PERSON>, Tabs } from "antd";
import { cloneDeep } from 'lodash';
import i18n from "i18next";
import { useTranslation } from "react-i18next";
import React, { useEffect, useMemo } from "react";
import { useDispatch, useSelector } from "src/hook";
import { setActiveTab, setImportDesConfigModalVisible } from "../DataProtectionPageSlice";
import { DEFAULT_KEY, SECURITY_CONFIG, TABS_PANE } from "../constant";
import { DispatchView } from "./dispatch";
import { TabKey } from "./type";
import styles from '../index.module.scss'
import ImportDesConfig from "./ImportDesConfig";
import { RenderTourStepContent, STEP_OPTION } from 'src/components';
import { setGuideSteps, setGuideVisible } from 'src/pageTabs/SceneGuideModal/guideSlice';
import { CommonEditDesensRulesModal, DesensitizedUserListModal } from './Dialog'
import StrategyModal from "src/pageTabs/automaticAuthorizationOperate/modal/StrategyModal";

const getSteps = (activeTab: string) => [
  {
    target: '.guide-protect-sdt',
    content: RenderTourStepContent({
      title: i18n.t('db.auth.dataProtection.guide.title'),
      detail: activeTab === 'DESENS' ? 
      i18n.t('db.auth.dataProtection.guide.content1')
      : i18n.t('db.auth.dataProtection.guide.content2')
     }),
    ...STEP_OPTION,
    placement: 'right-start'
  }
]

const DataProtectionMain = (): JSX.Element => {
  const defaultActiveKey = DEFAULT_KEY;
  const dispatch = useDispatch();
  const { t } = useTranslation();
	const { guideUniqueIds } = useSelector(state => state.guide);
  const { selectedNode, activeTab, importDesConfigModalVisible } = useSelector((state) => state.dataProtection);
  
  const { nodeType = '' } = selectedNode?.props ?? {} as any;
  
  const handleChange = (e: string) => dispatch(setActiveTab(e))
  
  const TABS_PANE = {
    DESENS: t('dataProtection.desensField'),
    DATA_INFO: t('dataProtection.dataInfo'),
    FILTER_ROW: t('dataProtection.tab.filter'),
    // HIGHRISKFILTER: '红线数据过滤',
    SCAN: t('dataProtection.tab.scan'),
    SECURITY: t('dataProtection.tab.security'),
  }

  const filteredTabs = useMemo(() => {
    if (!nodeType) return TABS_PANE;

    let newTabs = cloneDeep(TABS_PANE);
    
    if (nodeType && nodeType === "table") {
      newTabs["DESENS"] = t('db.auth.dataProtection.tab.desens');
      //@ts-ignore
      delete newTabs['DATA_INFO']
    } 
    if (SECURITY_CONFIG.includes(nodeType)) {
      //@ts-ignore
      delete newTabs.DATA_INFO;
      //@ts-ignore
      delete newTabs.DESENS;
      //@ts-ignore
      delete newTabs.FILTER_ROW;
      //@ts-ignore
      delete newTabs.SCAN;
    }
    return newTabs;
  }, [TABS_PANE, nodeType]);
  
  useEffect(() => {
    const allTabKeys = Object.keys(filteredTabs);
    // 保持跟上次时候选择过的tab，如果当前tab不包含，则切第一个
    if (!allTabKeys.includes(activeTab)) {
      handleChange(allTabKeys[0])
    }
  }, [filteredTabs])

  useEffect(() => {
		
    if (guideUniqueIds?.length && guideUniqueIds.includes('DATA_PROTECT_SDT')) {
     dispatch(setGuideSteps({steps: getSteps(activeTab), guideUniqueId: 'DATA_PROTECT_SDT'}));
     dispatch(setGuideVisible(true));
    }
  },[guideUniqueIds, activeTab])

  return (
    <div  className={styles.dataProtectWrap} style={{backgroundColor: '#fff', height: '100%'}} key={selectedNode?.key}>
      {
        importDesConfigModalVisible ?
         <ImportDesConfig onCallback={() => {dispatch(setImportDesConfigModalVisible(false)) }}/>
         :
         <>    
            <Tabs
              defaultActiveKey={defaultActiveKey}
              tabPosition="top"
              animated
              onChange={handleChange}
              activeKey={activeTab}
            >
              {Object.keys(filteredTabs).map((key) => (
                <Tabs.TabPane tab={filteredTabs[key as TabKey]} key={key} />
              ))}
            </Tabs>
            <div  className={styles.viewContent}>
            { 
              activeTab === "FILTER_ROW" && 
              <Alert
                message={t('db.auth.dataProtection.tab.filter.tip')}
                type="info"
                showIcon
                style={{marginBottom: 10}}
              />
            }
              <DispatchView />
            </div>
            {/* 编辑脱敏字段 公用*/}
            <CommonEditDesensRulesModal />
            {/* 策略修改 */}
            <StrategyModal />
            {/* 申请脱敏用户列表 */}
            <DesensitizedUserListModal />
         </>
      }
    </div>
  );
};

export { DataProtectionMain };

