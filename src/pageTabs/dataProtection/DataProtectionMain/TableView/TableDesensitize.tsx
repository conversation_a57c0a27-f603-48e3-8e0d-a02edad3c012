import {
	Button,
	message,
	Space,
	Table,
	Tag,
	Switch,
	Modal,
	Input,
  Tooltip
} from 'antd';
import { ColumnsType } from 'antd/lib/table';
import { useTranslation } from 'react-i18next';
import React, { useEffect, useState, useMemo } from 'react';
import { ExclamationCircleFilled, SearchOutlined } from '@ant-design/icons';
import {
	deleteDesensResource,
	getDesensitizeResourceList,
	setDesensitizeResourceStatus,
	ViewType
} from 'src/api/dataProtection';
import { RenderTourStepContent, STEP_OPTION } from 'src/components';
import { getRowFilterTableInfo } from 'src/api/rowFilter';
import { useDispatch, useRequest, useSelector } from 'src/hook';
import { showModal } from 'src/store/extraSlice/modalVisibleSlice';
import { setGuideSteps, setGuideVisible } from 'src/pageTabs/SceneGuideModal/guideSlice';
import { setColumnType, setNodePathOfColumn } from '../../DataProtectionPageSlice';
import { EditDesensitization } from '../Dialog/EditDesensitization';
import { formatRuleName } from '../utility';
import { setDesensRuleSettingObj } from '../../DataProtectionPageSlice'
import styles from './index.module.scss';
import classnames from 'classnames';

interface TableType {
	columnName: string;
	comments: string;
	dataType: string;
  dataLength: string;
	example: string;
	ruleName?: string;
	nodePathWithType: string;
	desensitizeable?: boolean;
}

interface DesensitizeType {
	id: number;
	connectionName: string;
	nodePath: string;
	columnName: string;
	ruleName: string;
	ruleParam: string;
	status: boolean;
	desensitizeable?: boolean;
}

const { confirm} = Modal;
/**
 * 渲染 table 列信息
 * @returns ColumnsType<DataType>
 */
const RenderTableColumns = ({
	refresh
}: {
	refresh: () => Promise<any>
}, permissionSupportData: boolean,
  permissionlist: {
    isOnlyRead: boolean
    roleNameList: string[]
  }): ColumnsType<TableType> => {
	const dispatch = useDispatch();
	const { t } = useTranslation();

	return [
		{
			dataIndex: "columnName",
			key: "columnName",
			title: t('dataProtection.desens.columnName'),
			ellipsis: true,
		},
		{
			dataIndex: "comments",
			key: "comments",
			title: t('dataProtection.desens.comments'),
			ellipsis: true,
		},
		{
			dataIndex: "dataType",
			key: "dataType",
			title: t('dataProtection.desens.fieldType'),
			ellipsis: true,
      render(_, record) {
        const { dataType, dataLength } = record ?? {};
        return dataLength ? `${dataType} (${dataLength})` : `${dataType}`;
      },
		},
		{
			key: "ruleName",
			dataIndex: 'ruleName',
			title: t('dataProtection.desens.ruleName'),
            render: (val: string) => val ? formatRuleName(val) : '-'
		},
		{
			dataIndex: "example",
			key: "example",
			title: t('dataProtection.desens.example'),
			ellipsis: true,
		},
		{
			key: "action",
			title: t('common.text.action'),
			render(value, record) {
				return  (
					<Space className='guide-protect-desens-field'>
						{
							record.ruleName ?
                <Tooltip title={
                  !permissionSupportData?
									t('dataProtection.noPerm', {roleNameList:permissionlist?.roleNameList?.join(", ") })
                  : null
                  } 
                >
                  <span
                    className={classnames(styles.linkBtn, !permissionSupportData? styles.disabled : '')}
                    onClick={() => {
                      if (!permissionSupportData) return 
                      dispatch(showModal("CommonEditDesensRulesModal"));
                      dispatch(
                        setDesensRuleSettingObj({
                          refresh,
                          editRecord: record,
                        })
                      );
                    }}
                  >
                    {t('dataProtection.desens.editRule')}
                  </span>
                </Tooltip>
                :
                <Tooltip title={
                  !permissionSupportData?
									t('dataProtection.noPerm', {roleNameList:permissionlist?.roleNameList?.join(", ") })
                  : null
                  }
                >
                  <span
                    className={classnames(record?.desensitizeable ? styles.linkBtn: styles.disabledBtn, 
                      !permissionSupportData? styles.disabled : '',
                      )}
                    onClick={() =>  {
                      if (!record?.desensitizeable || !permissionSupportData) return 
                      dispatch(setColumnType(record.dataType));
                      dispatch(setNodePathOfColumn(record.nodePathWithType));
                      dispatch(showModal("DesensitizationDialog"));
                      dispatch(
                        setDesensRuleSettingObj({
                          viewRecord: record,
                        })
                      );
                    }}
                  >
                    {t('dataProtection.desens.setRule')}
                  </span>
                </Tooltip>
						}
					</Space>
				)
			},
		},
	];
};

/**
 * 渲染脱敏规则列信息
 */
const RenderDesensitizeColumns = ({ refresh }: { refresh: () => Promise<any> },
  permissionSupportData: boolean,
  permissionlist: {
    isOnlyRead: boolean
    roleNameList: string[]
  }): ColumnsType<DesensitizeType> => {
	const dispatch = useDispatch();
	const { t } = useTranslation();

	const { run: setStatus } = useRequest(setDesensitizeResourceStatus, {
		manual: true,
	});

	const { run: deleteResource } = useRequest(deleteDesensResource, {
		manual: true,
		onSuccess() {
			message.success(t('common.message.delete_success'));
		},
	});

	async function handleSetStatus(record: DesensitizeType) {
		await setStatus({
			id: [record.id],
			status: !record.status,
		});
		refresh();
	}
	const showDeleteConfirm = (record: any) => {
		confirm({
		  title: t('db.auth.connectSetting.tab.users.deleteTool.tip'),
		  icon: <ExclamationCircleFilled />,
		  async onOk() {
			await deleteResource(record.id);
		refresh();
		  },
		});
	  };


	return [
		{
			dataIndex: "columnName",
			key: "columnName",
			title: t('dataProtection.desens.columnName'),
			ellipsis: true,
		},
		{
			dataIndex: "ruleName",
			key: "ruleName",
			title: t('dataProtection.desens.ruleName'),
			ellipsis: true,
			render(value) {
				return formatRuleName(value);
			},
		},
		{
			dataIndex: "ruleParam",
			key: "ruleParam",
			title: t('dataProtection.desens.ruleParam'),
			ellipsis: true,
		},
		{
			dataIndex: 'source',
			title: t('dataProtection.desens.source'),
		  },
		{
			dataIndex: "status",
			key: "status",
			title: t('dataProtection.desens.status'),
			render: (value, record) =>
      <Tooltip title={
        !permissionSupportData?
				t('dataProtection.noPerm', {roleNameList:permissionlist?.roleNameList?.join(", ") })
        : null
        }
      >
        <Switch 
          checked={value ? true : false}
          checkedChildren={t('dataProtection.desens.densesStatus.TRUE')}
          unCheckedChildren={t('dataProtection.desens.densesStatus.FALSE')}
          onChange={(checked: boolean) => {
            handleSetStatus(record)
          }}
        disabled={!permissionSupportData}
        />
      </Tooltip>
      ,
		},
		{
			dataIndex: "options",
			key: "options",
			title: t('common.text.action'),
			render(value, record) {
				return <Space size='small' align='start'>
        <Tooltip title={
          !permissionSupportData?
					t('dataProtection.noPerm', {roleNameList:permissionlist?.roleNameList?.join(", ") })
          : null
          }
        >
          <span className={classnames(styles.linkBtn, !permissionSupportData? styles.disabled : '')}
            onClick={() => {
              if (!permissionSupportData) return
              dispatch(showModal('CommonEditDesensRulesModal'))
              dispatch(setDesensRuleSettingObj({
                refresh,
                editRecord: record
              }))
            }} >
            {t('common.btn.edit')}
          </span>
        </Tooltip>
        <Tooltip title={
          !permissionSupportData?
					t('dataProtection.noPerm', {roleNameList:permissionlist?.roleNameList?.join(", ") })
          : null
          }
        >
          <span className={classnames(styles.deleteBtn, !permissionSupportData? styles.disabled : '')}
            onClick={() => {
              if (!permissionSupportData) return
              showDeleteConfirm(record)
            }}
          >
            {t('common.btn.delete')}
          </span>
        </Tooltip>
				</Space>
			  },
		},
	];
};

const TableDesensitize = (): JSX.Element => {
	
	const dispatch = useDispatch();
	const { t } = useTranslation();

  const { guideUniqueIds } = useSelector(state => state.guide);
	const isLangEn = useSelector(state => state.login.locales) === 'en';
	const { selectedNode, selectedNodePermissionInfo } = useSelector(state => state.dataProtection);
  const {modulePermissionObj, permissionSupportData } = selectedNodePermissionInfo["DATA_PROTECT"] || {};

	const [searchParams, setSearchParams] = useState<string>();
	const steps = [
		{
			target: '.guide-protect-desens-field',
			content: RenderTourStepContent({
				title: t('dataProtection.desens.setRule'),
				detail: t('dataProtection.desens.guide.content')
			 }),
			...STEP_OPTION,
			placement: 'left-start'
		}
	]

	const {
		run: getResourceList,
		loading: secondTableLoading,
		data: resourceList,
		refresh: refreshRuleTable,
	} = useRequest(getDesensitizeResourceList, {
		manual: true,
	});

	const {
		run: getTableInfo,
		loading: firstTableLoading,
		data: tableInfoData,
		refresh: refreshInfoTable,
	} = useRequest(getRowFilterTableInfo, {
		manual: true,
	});

	function generateTableData() {
		if (!tableInfoData || tableInfoData.length === 0) {
			return;
		}

		return tableInfoData.map((tableInfo: { nodePathWithType: string; nodeOptions: any; nodeName: string; desensitizeable?: boolean }) => {
			const target = resourceList?.find((resource: { nodePath: string }) => {
				return tableInfo.nodePathWithType === resource.nodePath;
			});

			if (target) {	
				return {
					columnName: tableInfo.nodeName,
					comments: tableInfo.nodeOptions.comments,
					dataType: tableInfo.nodeOptions.dataType,
          dataLength: tableInfo.nodeOptions?.dataLength,
					example: tableInfo.nodeOptions.example,
					isNullable: tableInfo.nodeOptions.isNullable,
					nodePathWithType: tableInfo.nodePathWithType,
					ruleName: target.ruleName,
					ruleParam: target.ruleParam,
					algoParam: target.algoParam || {},
					columnType: target.columnType,
					id: target.id,
					desensitizeable: tableInfo?.desensitizeable
				};
			} else {
				return {
					columnName: tableInfo.nodeName,
					comments: tableInfo.nodeOptions.comments,
					dataType: tableInfo.nodeOptions.dataType,
          dataLength: tableInfo.nodeOptions?.dataLength,
					example: tableInfo.nodeOptions.example,
					isNullable: tableInfo.nodeOptions.isNullable,
					nodePathWithType: tableInfo.nodePathWithType,
					desensitizeable: tableInfo?.desensitizeable
				};
			}
		});
	}

	function refreshAll() {
		return Promise.all([refreshInfoTable(), refreshRuleTable()]);
	}

	useEffect(() => {
		if (selectedNode) {
			const { connectionId, nodePath, nodePathWithType, nodeType: findType, connectionType, nodeName } = selectedNode.props;

			getResourceList({
				connectionId: +connectionId as number,
				nodePath: nodePathWithType,
				findType: findType as ViewType,
			});

			getTableInfo({
				connectionId,
				connectionType,
				nodeType: findType,
				nodeName,
				nodePath,
				nodePathWithType: nodePathWithType,
			});
		}
	}, [getResourceList, JSON.stringify(selectedNode), getTableInfo]);


	const filteredDesensFieldList = useMemo(() => {
		const list = generateTableData();
      if (!searchParams) return list;
	   return list?.filter((item: any) => item?.columnName?.toLowerCase().includes(searchParams?.toLowerCase()))
	},[generateTableData, searchParams])

  useEffect(() => {

    if (guideUniqueIds?.length && guideUniqueIds?.length === 1 && guideUniqueIds.includes('DATA_PROTECT_DESENS') && filteredDesensFieldList?.length) {
     dispatch(setGuideSteps({steps, guideUniqueId: 'DATA_PROTECT_DESENS'}));
     dispatch(setGuideVisible(true));
    }
  },[guideUniqueIds,JSON.stringify(filteredDesensFieldList) ])

	return (
		<>
		 <div className={styles.desensFieldHeader}>
				<div className={styles.title}>{t('dataProtection.desens.ruleManage')}</div>
				<Input
				  allowClear
					prefix={<SearchOutlined />}
					placeholder={t('dataProtection.desens.searchField')}
					className={isLangEn ? styles.inputSearchEn : styles.inputSearch}
					onChange={(e: any) => setSearchParams(e.target.value)}
				/>
		 </div>
			<Table
				style={{ marginBottom: "40px" }}
				columns={RenderTableColumns({refresh: refreshRuleTable}, permissionSupportData, modulePermissionObj)}
				loading={firstTableLoading}
				dataSource={filteredDesensFieldList}
				pagination={false}
				scroll={{y: `calc(100vh / 2  - 220px)`}}
			></Table>
			<div>
			<div className={styles.title}>{t('dataProtection.desens.desensFieldDetail')}</div>
			<Table
				style={{ marginBottom: "80px",marginTop: 16 }}
				columns={RenderDesensitizeColumns({ refresh: refreshRuleTable }, permissionSupportData, modulePermissionObj)}
				loading={secondTableLoading}
				dataSource={resourceList}
				pagination={false}
				scroll={{y: `calc(100vh / 2  - 220px)`}}
			></Table>
			</div>
		
			<EditDesensitization refresh={() => refreshAll()} />
		</>
	);
};

export { TableDesensitize };

