import {
  Modal,
  Switch,
  Space,
  Select,
  Input,
  Dropdown,
  Button,
  Menu,
  message,
  Tooltip,
} from 'antd';
import * as _ from 'lodash';
import {
  ExclamationCircleFilled,
  DownOutlined,
  SearchOutlined,
  ExclamationCircleOutlined,
} from '@ant-design/icons';
import { ColumnsType } from 'antd/lib/table';
import { useTranslation } from "react-i18next";
import React, { useEffect, useState } from 'react';
import {
  setDesensitizeResourceStatus,
  batchSetPolicy,
  batchDeleteDesensResource,
  IPolicyParams,
  getFlowPermission,
  deleteFlowPermission,
  desensitizeResourceExport
} from 'src/api';
import { useDispatch, useRequest, useSelector } from 'src/hook';
import {
  formatRuleName,
  formatSelectOptions,
  formatStatusSelectOptions,
} from '../utility';
import { Iconfont, SimpleTable, TextWithTooltip, exportTaskCreatedNot } from 'src/components';
import { hideModal, showModal } from 'src/store/extraSlice/modalVisibleSlice';
import {
  setDesensRuleSettingObj,
  setDesensPolicySettingObj,
  setDesensitizedUserListObj,
} from '../../DataProtectionPageSlice';
import styles from './index.module.scss';
import classnames from 'classnames';

interface DataType {
  id: number;
  connectionName: string;
  nodePath: string;
  columnName: string;
  ruleName: string;
  ruleParam: string;
  status: boolean;
  datasourceType: string
}

const { confirm } = Modal;

const RenderColumns = ({
  refresh,
  onDelete,
  onSetPolicy,
}: {
  refresh: () => Promise<any>;
  onDelete: (ids: number[], fields: {nodePathWithType: string, dataSourceType: string}[]) => void;
  onSetPolicy: (params: IPolicyParams) => void;
}, permissionlist: any, permissionSupportData: boolean): ColumnsType<DataType> => {
  const dispatch = useDispatch();
  const { t } = useTranslation();
  const { roleNameList = []} = permissionlist || {}
  const isLangEn = useSelector(state => state.login.locales) === 'en'
  const { run: setStatus } = useRequest(setDesensitizeResourceStatus, {
    manual: true,
  });

  async function handleSetStatus(record: DataType) {
    await setStatus({
      id: [record.id],
      status: !record.status,
    });
    refresh();
  }

  return [
    {
      dataIndex: 'connectionName',
      title: t('dataProtection.desens.connectionName'),
      fixed: 'left',
      width: 200,
      render(value, record: any) {
        return (
          <span className='flexAlignCenter'>
            <Iconfont
              type={`icon-connection-${record?.datasourceType}`}
              style={{ marginRight: 4 }}
            />
            {value}
          </span>
        );
      },
    },
    {
      dataIndex: 'object',
      title: t('dataProtection.desens.object'),
      width: 150,
      render(value) {
        return <TextWithTooltip text={value} maxLength={20} />;
      },
    },
    {
      dataIndex: 'columnName',
      title: t('dataProtection.desens.columnName'),
      width: isLangEn ? 150 : 100,
    },
    {
      dataIndex: 'ruleName',
      title: t('dataProtection.desens.ruleName'),
      width: isLangEn ? 220 : 150,
      render(value) {
        return formatRuleName(value);
      },
    },
    {
      dataIndex: 'ruleParam',
      title: t('dataProtection.desens.ruleParam'),
      width: 110,
      ellipsis: true,
    },
    {
      dataIndex: 'source',
      title: t('dataProtection.desens.source'),
    },
    {
      dataIndex: 'source',
      title: t('dataProtection.desens.applyUser'),
      width:  isLangEn ? 250 : 150,
      render: (_: string, record: any) => (
        <span
          className={styles.linkBtn}
          onClick={() => {

            dispatch(setDesensitizedUserListObj({
              ...record,
              callback: () => refresh(),
            }));
            dispatch(showModal('DesensitizedUserListModal'));
          }}
        >
          {t('common.btn.view')}
        </span>
      ),
    },
    {
      dataIndex: 'policy',
      title: (
        <>
          {t('dataProtection.desens.editPolicy')}
          <Tooltip title={t('dataProtection.desens.editPolicy.tip')} >
            <ExclamationCircleOutlined style={{ marginLeft: 3 }}/>
          </Tooltip>
        </>
      ),
      width: isLangEn ? 250 : 150,
      render: (val: string, record: any) => (
        <Tooltip title={
          !permissionSupportData?          
          t('dataProtection.noPerm', { roleNameList: roleNameList.join(", ")})
          : null
          } 
        >
          <Button
            type='link'
            className={classnames(styles.linkBtn, styles.padding0)}
            onClick={() => {
              dispatch(showModal('StrategyModal'));
              dispatch(
                setDesensPolicySettingObj({
                  type: 'edit',
                  policy: val,
                  callback: (policy: string) =>
                    onSetPolicy({ resourceIds: [record.id], policy }),
                })
              );
            }}
            disabled={!permissionSupportData}
          >
            {t('dataProtection.desens.epolicy')}
          </Button>
        </Tooltip>
      ),
    },
    {
      dataIndex: 'status',
      title: t('dataProtection.desens.status'),
      width: 120,
      render: (value, record) => (
        <Tooltip title={
          !permissionSupportData?
          t('dataProtection.noPerm', { roleNameList: roleNameList.join(", ")})
          : null
          } 
        >
          <Switch
            checked={value ? true : false}
            checkedChildren={t('common.btn.enable')}
            unCheckedChildren={t('common.btn.off')}
            onChange={(checked: boolean) => {
              handleSetStatus(record);
            }}
            disabled={!permissionSupportData}
          />
        </Tooltip>
      ),
    },
    {
      dataIndex: 'options',
      title: t('common.text.action'),
      width: 120,
      fixed: 'right',
      render(value, record) {
        return (
          <Space size="small" align="start">
            <Tooltip title={
              !permissionSupportData?
              t('dataProtection.noPerm', { roleNameList: roleNameList.join(", ")})
              : null
              } 
              className={styles.displayFlex}
            >
              <Button
                type='link'
                className={classnames(styles.linkBtn, styles.padding0)}
                onClick={() => {
                  dispatch(showModal('CommonEditDesensRulesModal'));
                  dispatch(
                    setDesensRuleSettingObj({
                      refresh,
                      editRecord: record,
                    })
                  );
                }}
                disabled={!permissionSupportData}
              >
                {t('common.btn.edit')}
              </Button>
              <Button
                type='link'
                className={classnames(styles.deleteBtn, styles.padding0)}
                onClick={() => onDelete([record.id], [{nodePathWithType:record?.nodePath, dataSourceType: record?.datasourceType}])}
                disabled={!permissionSupportData}
              >
                {t('common.btn.delete')}
              </Button>
            </Tooltip>
          </Space>
        );
      },
    },
  ];
};

const RootDesensitize = ({
  loading,
  dataSource,
  isSelectedRowIndex,
  refresh,
  getParams,
}: {
  loading: boolean;
  dataSource: any;
  isSelectedRowIndex?: null | number;
  refresh: () => Promise<any>;
  getParams: (a: any) => void;
}): JSX.Element => {

  const dispatch = useDispatch();

  const { t } = useTranslation();
  const { selectedNode, selectedNodePermissionInfo ={} } = useSelector((state) => state.dataProtection);

  const {modulePermissionObj = {isOnlyRead: false, roleNameList: []}, permissionSupportData = true } = selectedNodePermissionInfo["DATA_PROTECT"] || {};

  const iniPageData = {pageSize: 10, current: 1};
  const [searchParams, setSearchParams] = useState<{
    search: string;
    status?: any;
    pageSize: number;
    current: number;
  }>({ status: 'ALL', ...iniPageData, search: ''});

  const [selectedRowKeys, setSelectedRowKeys] = useState<number[]>([]);
  const [selectedRowItems, setSelectedRowItems] = useState<any>([]);
  const [searchValue, setSearchValue] = useState<string>('');

  //批量设置策略
  const { run: runBatchSetPolicy } = useRequest(batchSetPolicy, {
    manual: true,
    onSuccess: (res) => {
      message.success(t('dataProtection.desens.editPolicy.success'));
      setSelectedRowKeys([]);
      refresh();
      dispatch(hideModal('DesensPolicySettingModal'));
    },
  });
  //批量删除
  const { run: batchDeleteDesens } = useRequest(batchDeleteDesensResource, {
    manual: true,
    onSuccess: () => {
      message.success(t('common.message.delete_success'));
      refresh();
      setSelectedRowKeys([]);
    },
  });

  // 批量 启用/禁用
  const { run: setStatus } = useRequest(setDesensitizeResourceStatus, {
    manual: true,
  });

  // 批量导出
  const { run: runBathDesensitizeResourceExport } = useRequest(desensitizeResourceExport, {
    manual: true,
    onSuccess: () => {
      exportTaskCreatedNot();
    }
  });

  useEffect(() => {
    setSearchValue("");
    setSearchParams((params: any) => {
      return {...params, ...iniPageData}
    })
    setSelectedRowKeys([]);
    setSelectedRowItems([]);
  }, [selectedNode?.key]);

  useEffect(() => {
    getParams(searchParams)
  }, [searchParams])

  const showDeleteConfirm = async({
    type,
    resourceIds,
    fields
  }: {
    type: 'EXPORT' | 'DELETE' | any;
    resourceIds: number[];
    fields?: {
      nodePathWithType: string;
      dataSourceType: string;
    }[]
  }) => {
    // 批量编辑策略
    if (type === 'POLICY') {
      dispatch(showModal('StrategyModal'));
      dispatch(
        setDesensPolicySettingObj({
          type: 'edit',
          isBatch: true,
          callback: (policy: string) => 
            runBatchSetPolicy({ resourceIds, policy }),
        })
      );
      return;
    }

    // 批量编辑
    if ( type === 'EDIT') {
      const columnTypes = selectedRowItems?.map((item: any) => item?.columnType);
      const resetAfterSuccess = () => {
        refresh();
        setSelectedRowKeys([]);
        setSelectedRowItems([]);
      }
      dispatch(showModal('CommonEditDesensRulesModal'));
        dispatch(
          setDesensRuleSettingObj({
            refresh: resetAfterSuccess,
            editRecord: {isBatch: true, columnType: columnTypes, id: selectedRowKeys},
          })
        );
  
      return
    }

    
    confirm({
      title: t('dataProtection.desens.action.tip', {action: type === 'DELETE' ? t('common.btn.delete') : type === 'EXPORT' ? t('common.btn.export') : type === 'TRUE' ? t('common.btn.enable') : t('common.btn.forbidden'), count:  resourceIds?.length}),
      icon: <ExclamationCircleFilled />,
      content: type=== 'DELETE' && t('dataProtection.desens.action.content'),
      onOk() {
        if (type === 'EXPORT') {
          runBathDesensitizeResourceExport(resourceIds);
          setSelectedRowKeys([]);
        } else if (type === 'DELETE') {
          //循环 查看脱敏用户 并删除
          const desensUsers = fields?.map(async (item) => {
            return await getFlowPermission(item);
          });
          desensUsers && Promise.all(desensUsers)
          .then(async(results) => {
            let permissionIds: number[] = [];
            results?.map(result => {
              const ids = result?.map((item: any) => item?.permissionId);
              permissionIds = permissionIds.concat(ids);
            });
            //删除
            try {
              if (permissionIds?.length) {
                await deleteFlowPermission({permissionIds})
              }
             
              //获取所有permissionIds
              await batchDeleteDesens({ resourceIds });
            } catch (error) {
              throw new Error()
            }
            
          })
          .catch((error) => {
            console.error(error);
          });
        
        } else if (type === 'TRUE') {
          try {
            if (resourceIds?.length) {
              setStatus({
                id: resourceIds,
                status: true
              }).then(() => {
                setSelectedRowKeys([]);
                refresh()
              })
            }
          } catch (error) {
            throw new Error()
          }
        } else if (type === 'FALSE') {
          try {
            if (resourceIds?.length) {
              setStatus({
                id: resourceIds,
                status: false
              }).then(() => {
                setSelectedRowKeys([]);
                refresh()
              })
            }
          } catch (error) {
            throw new Error()
          }
        }
      },
    });
  };

  return (
    <div className={styles.rootDesensitizePart}>
      <div className={styles.desensHeader}>
        <Space>
          {t('dataProtection.desens.status2')}
          <Select
            options={formatStatusSelectOptions()}
            defaultValue="ALL"
            onChange={(type: string) => {
              const cloneSearchParams = _.cloneDeep(searchParams);
              setSearchParams({
                ...cloneSearchParams,
                status: type,
              });
            }}
          />
          <Input
            allowClear
            prefix={<SearchOutlined />}
            className={styles.inputSearch}
            placeholder={t('dataProtection.desens.search')}
            onChange={(e: any) => {
              const cloneSearchParams = _.cloneDeep(searchParams);
              setSearchParams({
                ...cloneSearchParams,
                search: e.target.value,
              });
              setSearchValue(e.target.value);
            }}
            style={{width: 350}}
          />
          <Dropdown
            disabled={selectedRowKeys?.length === 0 || !permissionSupportData}
            overlay={
              <Menu
                className={styles.options}
                onClick={(e) => {
                  const fields = selectedRowItems?.map((item: any) => ({nodePathWithType: item?.nodePath, dataSourceType: item?.datasourceType}))
                    showDeleteConfirm({
                      type: e.key,
                      resourceIds: selectedRowKeys,
                      fields
                    })
                  }
                }
              >
                {formatSelectOptions.map((item) => (
                  <Menu.Item key={item?.value}>{item?.label}</Menu.Item>
                ))}
              </Menu>
            }
          >
            <Tooltip title={
              !permissionSupportData?
               t('dataProtection.noPerm', { roleNameList: modulePermissionObj?.roleNameList?.join(", ")})
              : null
              } 
            >
              <Button type="primary" disabled={!permissionSupportData || selectedRowKeys?.length < 1}>
              <Space>
                {t('common.btn.batchText')}
                <DownOutlined />
              </Space>
            </Button>
            </Tooltip>
          </Dropdown>
        </Space>
      </div>
      <div className={styles.desensFieldTable}>
        <SimpleTable
          rowKey="id"
          rowClassName={(record, index) => index === isSelectedRowIndex ? 'globalSearchRowSelected' : ''}
          total={dataSource?.total}
          columns={RenderColumns({
            refresh,
            onDelete: (ids: number[], fields:{nodePathWithType: string, dataSourceType: string}[]) =>
              showDeleteConfirm({ type: 'DELETE', resourceIds: ids, fields }),
            onSetPolicy: (params: IPolicyParams) => runBatchSetPolicy(params),
          }, modulePermissionObj, permissionSupportData)}
          loading={loading}
          dataSource={dataSource?.list}
          showPagination={true}
          scroll={{ x: 'max-content', y: `calc(100vh - 410px)` }}
          rowSelection={{
            selectedRowKeys,
            onChange: (selectedRowKeys, selectedRows) => {
              setSelectedRowKeys(selectedRowKeys as number[]);
              setSelectedRowItems(selectedRows)
            } 
          }}
          searchParams={searchParams}
          setSearchParams={(params: any) => {
            const cloneParams = _.cloneDeep(params);
            cloneParams.current = cloneParams?.currentPage;
            delete cloneParams?.currentPage
            setSearchParams({...cloneParams, search: searchValue})
          }}
        />
      </div>
    </div>
  );
};

export { RootDesensitize };
