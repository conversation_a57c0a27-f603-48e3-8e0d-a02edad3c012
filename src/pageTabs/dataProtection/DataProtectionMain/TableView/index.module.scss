.desenHeader {
  width: 100%;
  display: flex;
  align-items: center;
  justify-content: space-between;

  .desenSetting {
    font-weight: 700;
    margin-right: 10px;
  }

  .flexrc {
    display: flex;
    justify-content: center;
    align-items: center;
  }
}

.deleteBtn {
  color: #EA0000;
  cursor: pointer;
}

.linkBtn {
  color: #3262FF;
  cursor: pointer;
}

.disabledBtn {
  color: #d9d9d9;
  cursor: pointer;
}

.rootDesensitizePart {

  .desensFieldTable {
    height: calc(100% - 32px);
  }
}

.desensHeader {
  text-align: right;
  margin-bottom: 16px;
}

.inputSearch {
  width: 150px;
}

.inputSearchEn {
  width: 250px;
}

.desensFieldHeader {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 16px;

  .title {
    color: #0F244C;
    font-size: 14px;
    font-weight: 500;

  }
}

.displayFlex {
  display: flex;
}

.disabled {
  color: #B3B3B3;
  cursor: not-allowed;
}

.securitySetting {
  .mr {
    margin-right: 10px;
  }

  .mr5 {
    margin-right: 5px;
  }

  .ml25 {
    margin-left: 25px;
  }

  .fontColor {
    color: rgba(102, 112, 132, 1.6);
  }

  .hightRiskType {
    padding-left: 10px;

    :global {
      .ant-radio-wrapper {
        margin-bottom: 6px;
      }
    }
  }
}

.highRiskOperation {
  display: flex;
  flex-direction: column;
  height: 184px;
  overflow-y: auto;
  padding: 10px 10px;
  border-radius: 3px;
  border: 1px solid rgba(135, 139, 146, 0.15);
  margin: 0 0 10px 10px;

  .operations {
    display: flex;
    align-items: center;
    justify-content: flex-start;
    width: 100%;
    flex-wrap: wrap;

    :global {
      .ant-tag {
        margin: 4px 2px;
      }
    }
  }

  .addBtn {
    min-height: 80px;
    line-height: 80px;
    width: 100%;
    text-align: center;
    margin: auto;
    color: #3262ff;
    cursor: pointer;
  }
}

.hightRiskAlert {
  margin-bottom: 10px;
}

.highRiskOperationSetting {
  .searchInput {
    width: 150px;
    margin-right: 6px;
  }

  .rightCol {
    position: absolute;
    right: 0px;
  }

  .tableTop {
    margin-top: 12px;
  }

  .customTable {
    :global {
      .ant-table-tbody>tr>td {
        padding: 7px 5px 7px 5px;
        font-size: 13px;
      }
    }

  }

  .highRiskWindowsAlert {
    margin-bottom: 12px;
  }

}

.securitySettingOperation {
  .mb10 {
    margin-bottom: 10px;
  }

  .mr8 {
    margin-right: 8px;
  }

  .customAuthorizeTab {
    :global {
      .ant-tabs-content {
        max-height: 46vh;
        overflow-y: auto;
      }
    }
  }

  .options {
    cursor: pointer;
    color: var(--primary-color);

    :global {
      .ant-dropdown-menu-item {
        color: var(--primary-color);
        max-width: 300px;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
      }

      .ant-dropdown-menu-item-disabled,
      .ant-dropdown-menu-submenu-title-disabled {
        color: rgba(0, 0, 0, 0.25);
      }
    }
  }
}

.card {
  :global {
    .ant-tabs-tab {
      background-color: #f0f1f5 !important;
    }

    .ant-tabs-tab-active {
      background-color: #fff !important;
      border-radius: 4px !important;
    }

    .ant-tabs-nav-list {
      height: 32px;
      border-radius: 4px;
      padding: 3px !important;
      background-color: #f0f1f5 !important;
      margin-bottom: 16px;
    }

    .ant-tabs-tab.ant-tabs-tab-active .ant-tabs-tab-btn {
      color: #0f244c;
    }

    .ant-tabs-tab:hover {
      color: #0f244c;
    }

    .ant-tabs-tab-btn:active {
      color: #0f244c;
    }

    .ant-tabs-tab-btn {
      color: #868FA3;
    }

    .ant-tabs-nav-wrap {
      border-bottom: none !important;
      height: fit-content !important;
      padding-left: 0 !important;
    }
  }
}

.ml3 {
  margin-left: 3px;
}

.dropDownDisabled,
.dropDownDisabled:hover,
.dropDownDisabled:active,
.dropDownDisabled:focus {
  background-color: #f3f3f3;
  color: #cacadd;
  border-color: #cacadd;
}

.alertSty {
  position: fixed;
  top: 30px;
  left: 50%;
  transform: translateX(-50%);
  z-index: 1000;
  width: 300px;
}

.windowsLabel {
  display: inline-block;
  margin-right: 5px;
}
