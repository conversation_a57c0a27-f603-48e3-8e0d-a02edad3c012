import { Spin, Table } from 'antd'
import { debounce } from 'lodash'
import { useTranslation } from 'react-i18next'
import React, { forwardRef, useCallback, useEffect, useImperativeHandle, useState } from 'react'
import { DesensRuleName, getDesenPrivew, getReviewData } from 'src/api'
import { useRequest, useSelector } from 'src/hook'

interface IProps {
  form: any
  nodePathWithType: string
}
const PreviewDataTable = forwardRef((props: IProps, ref: any) => {

  const { form, nodePathWithType } = props;
  const initPreviewData = [{key: 0, before: '---', after: '---'}];
  const { t } = useTranslation();
  const { selectedNode } = useSelector(
    (state) => state.dataProtection,
  )

  const [previewData, setPreviewData] = useState<any[]>(initPreviewData); // 脱敏后数据
  const [desenPrivewLoading, setDesenPrivewLoading] = useState<boolean>(false);

  // 获取未脱敏的前十条数据
  const { data: reviewData, run: getReviewDataRun } = useRequest(getReviewData, { manual: true })

  // 获取脱敏后数据
  const { run: getDesenPrivewRun } = useRequest(getDesenPrivew, {
    manual: true,
    formatResult: async (res) => {
      let combinedArray = [];
      if (reviewData.length !== res.length) {
        setPreviewData(initPreviewData);
        return;
      }

      for (let i = 0; i < reviewData.length; i++) {
        let obj = {
          key: i,
          before: reviewData[i],
          after: res[i]
        };
        combinedArray.push(obj);
      }
      setPreviewData(combinedArray);
      setDesenPrivewLoading(false);
    },
    onError: () => {
      setDesenPrivewLoading(false);
      setPreviewData(initPreviewData);
    }
  })

  useEffect(() => {
    getReviewDataRun({
      connectionType: selectedNode?.props?.connectionType || '',
      nodePathWithType: nodePathWithType,
    })

    return () => {
      console.log('table 组件销毁 :>> ');
    }
  }, [])

  useEffect(() => {
    (reviewData?.length > 0) && updateParam()
  }, [reviewData])

  const getData = useCallback((params: {[p: string]: any}) => {
    //获取预览数据 
    const updatePreviewData = () => {
      const { ruleName, ruleParam } = params || {};
      const ruleParamParams = { ...ruleParam };
      if (ruleParamParams?.dateRange) {
        const { dateRange } = ruleParamParams as { dateRange: moment.Moment[] };
  
        ruleParamParams.min = +dateRange[0].format('x');
        ruleParamParams.max = +dateRange[1].format('x');
        delete ruleParamParams.dateRange;
      }

      getDesenPrivewRun({
        ruleName: ruleName as DesensRuleName,
        ruleParam: ruleParamParams || {},
        values: [...reviewData],
      })
    }

    // 获取预览数据
    if (reviewData?.length > 0) {
      setDesenPrivewLoading(true);
      debounce(updatePreviewData, 700)();
    }
  }, [getDesenPrivewRun, reviewData])

  // 更新参数
  const updateParam = async () => {
    form.validateFields().then((values: any) => {
      getData(values)
    }).catch((errorInfo: any) => {
      const {errorFields, values} = errorInfo;
      if (errorFields?.length > 0) {
        setPreviewData(initPreviewData);
      } else {
        getData(values);
      }
    })
  }

  // 更新参数（有延迟）
  const updateParamDelay = () => {
    const timer = setTimeout(async () => {
    
      updateParam();

    }, 600);
    // 组件卸载时取消定时器
    return () => {
      clearTimeout(timer);
    };
  }

  // 配置信息改变时
  const valuesChange = (changedValues: any) => {
    // 修改脱敏算法，脱敏算法的render延迟会导致form.validateFields()获取不到值，所以这里需要延迟获取值
    if (Object.keys(changedValues).includes("ruleName")) {
      updateParamDelay();
    } else {
      updateParam();
    }
  };

  // 使用 useImperativeHandle 公开函数给父组件使用
  useImperativeHandle(ref, () => ({
    valuesChange
  }));

  const columns = [
    {
      title: t('dataProtection.desens.predesens'),
      dataIndex: "before",
      key: "before",
      render: (v: any) => {
        return <span>{v || '-'}</span>
      }
    },
    {
      title: t('dataProtection.desens.afterDesens'),
      dataIndex: "after",
      key: "after",
      render: (v: any) => {
        return <span>{v || '-'}</span>
      }
    },
  ]

  return (
    <Spin spinning={desenPrivewLoading}>
      <Table
        rowKey="id"
        dataSource={previewData}
        columns={columns}
        pagination={false}
        size='small'
        bordered={true}
        scroll={{ y: `calc(100vh - 680px)` }}
      />
    </Spin>
  )
})

export default PreviewDataTable;