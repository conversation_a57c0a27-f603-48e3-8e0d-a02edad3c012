import { Button, Divider, Form, Select, message } from 'antd'
import React, { useEffect, useMemo, useRef, useState } from 'react'
import { useTranslation } from 'react-i18next'
import {
  DesensRuleName,
  getDesensitizeRules,
  updateDesensitizeRules,
} from 'src/api/dataProtection'
import { UIModal } from 'src/components'
import { FormLayout } from 'src/constants'
import { useDispatch, useRequest, useSelector } from 'src/hook'
import { hideModal } from 'src/store/extraSlice/modalVisibleSlice'
import { DesensitizationSettingItems } from './DesensitizationSettingItems'
import PreviewDataTable from './PreviewDataTable'

export const EditDesensitization = ({
  refresh,
}: {
  refresh: () => Promise<(() => Promise<any>)[]>
}): JSX.Element => {
  const [form] = Form.useForm()
  const { selectedNode, columnType, nodePathOfColumn, desensRuleSettingObj = {} } = useSelector(
    (state) => state.dataProtection,
  )
  const { viewRecord } = desensRuleSettingObj;

  const dispatch = useDispatch()
  const { t } = useTranslation()

  const [option, setOption] = useState<string>('')
  const [isPreview, setIsPreview] = useState(false)
  const previewDataTableRef = useRef<any>(null)
  
  const { run, loading } = useRequest(updateDesensitizeRules, {
    manual: true,
    onSuccess: async () => {
      message.success(t('db.auth.connectSetting.settingSuccess'))
      dispatch(hideModal('DesensitizationDialog'))
    },
  })

  // 脱敏算法
  const { data: selectOptions, run: getDenseRules } = useRequest(
    getDesensitizeRules,
    {
      manual: true,
      formatResult: (data) =>
        data.map(
          (item: {
            ruleType: string
            ruleParam: object
            values: string[]
            ruleName: string
          }) => {
            return {
              label: item.ruleName,
              value: item.ruleType,
              props: {
                ruleParam: item.ruleParam,
                options: item.values,
              },
            }
          },
        ),
    },
  )
  
  useEffect(() => {
    if (selectedNode && columnType) {
      const { connectionType: datasourceType } = selectedNode.props
      getDenseRules({ columnType, datasourceType })
    }
  }, [getDenseRules, selectedNode, columnType])

  async function submitForm(values: any) {
    // 日期时间格式分成两个字段传值
    if (values?.ruleParam?.dateRange) {
      const { dateRange } = values.ruleParam as { dateRange: moment.Moment[] }

      values.ruleParam.min = +dateRange[0].format('x')
      values.ruleParam.max = +dateRange[1].format('x')
      delete values.ruleParam.dateRange
    }

    if (selectedNode && columnType && nodePathOfColumn) {
      const { connectionType: datasourceType } = selectedNode.props
      const params = {
        nodePath: nodePathOfColumn,
        datasourceType,
        columnType,
        ruleParam: values.ruleParam,
        ruleName: option as DesensRuleName,
      }
      await run(params)
      await refresh()
      setOption('')
    }
  }

  const visible = useSelector((state) => state.modal.DesensitizationDialog?.visible || false)

  // 脱敏算法设置改变
  const handleValuesChange = (changedValues: any) => {
    setIsPreview((item: boolean) => {
      if (previewDataTableRef?.current) {
        previewDataTableRef?.current?.valuesChange(changedValues);
      }
      return item;
    })
  };

  const handleAfterClose = () => {
    form.resetFields()
    setIsPreview(false)
  }

   // 脱敏算法设置
  const renderDesensitizationSettingItems = useMemo(() => {
    if (!option) {
      form?.setFieldsValue({ ruleParam: {} })
    }
    return option.length > 0 && (
      <DesensitizationSettingItems
        option={option}
        form={form}
        values={selectOptions?.find((i) => i.value === option)?.props}
      />
    )
  }, [form, option, selectOptions])

  // 脱敏效果预览
  const renderPreviewDataTable = useMemo(() => {
    return (
      <PreviewDataTable
        ref={previewDataTableRef}
        form={form}
        nodePathWithType={viewRecord?.nodePathWithType}
      />
    )
  }, [form, viewRecord])

  return (
    <UIModal
      title={t('dataProtection.desens.setAlgorithm')}
      visible={visible}
      onOk={() => {
        form.submit()
      }}
      onCancel={() => {
        dispatch(hideModal('DesensitizationDialog'))
        setOption('')
      }}
      afterClose={handleAfterClose}
      confirmLoading={loading}
    >
      <Form
        form={form}
        onFinish={submitForm}
        {...FormLayout}
        onValuesChange={(changedValues) => handleValuesChange(changedValues)}
      >
        <Form.Item
          name="ruleName"
          label={t('dataProtection.desens.desensAlgorithm')}
          rules={[{ required: true, message: t('dataProtection.desens.desensAlgorithm.tip') }]}
        >
          <Select
            options={selectOptions as any}
            optionFilterProp="label"
            showSearch
            onChange={(v: string) => {
              setOption(v)
            }}
          ></Select>
        </Form.Item>
        {/* 脱敏算法设置 */}
        <Divider style={{ fontSize: '10px' }}>{t('dataProtection.desens.desensAlgorithm.title')}</Divider>
        {
          renderDesensitizationSettingItems
        }
        
        {/* 脱敏效果预览 */}
        {
          option && option !== "FILTER" && (
            isPreview ? (
              <Form.Item label={t('dataProtection.desens.desensPreview')}>
                {renderPreviewDataTable}
              </Form.Item>
            ) : (
              <Divider style={{ fontSize: '10px' }}>
                {t('dataProtection.desens.preview')}
                <Button
                  type='primary'
                  size='small'
                  style={{ marginLeft: '10px' }}
                  onClick={() => setIsPreview(true)}
                >
                  {t('common.btn.preview')}
                </Button>
              </Divider>
            )
          )
        }
      </Form>
    </UIModal>
  )
}
