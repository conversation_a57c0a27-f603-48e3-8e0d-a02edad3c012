import { createAsyncThunk, createSlice, PayloadAction } from '@reduxjs/toolkit'
import { batch } from 'react-redux'
import * as _ from 'lodash'
import { AppThunk, RootState } from 'src/store'
import {
  DataSourceType,
  getNodeChildList,
  openSdtNode,
  SdtNodeType,
  TreeNode,
  showSdtNodeView,
  NodeEntity,
  ResponseNodeEntity,
  getCallStatement,
  DataSourceWithAdditionalSchema,
  SdtResponse,
  openTaskExecution,
  openRefreshTask,
  ISearchSdtItem,
  getNodeBySearch
} from 'src/api'
import { getToolbarConnections } from 'src/store/extraSlice/editorSlice'
import { getInfoFromPath } from 'src/util'
import { addPane, PaneType } from '../queryTabs/queryTabsSlice'
import { formatTreeNode, formatTreeNodeWithGroupByType } from './const.sdt'

export const NodeTypesSupportEditorView: SdtNodeType[] = [
  'table',
  'view',
  'collection',
  'function',
  'procedure',
  'synonym',
  'sequence',
  'package',
  'type',
  'packageBody',
  'materializedView',
  'syncMaterializedView',
  'asyncMaterializedView',
  'trigger',
]

export interface IBatchCreateConnectionLog {
  name: 'VERIFY' | 'EXECUTE' | 'SUCCESS' | 'FAILED'
  content: string
  createTime: number
}

interface SdtState {
  treeData: TreeNode[]
  selectedNode: TreeNode
  mulSelectedNodes: TreeNode[] //多选的selectNodes
  // todo: 理清 selected 和 rightCliicked 关系
  rightClickedNode: Partial<TreeNode>
  expandedKeys: (string | number)[]
  loadedKeys: (string | number)[]
  treeLoading: boolean
  batchCreateConnectionLog: IBatchCreateConnectionLog[]
  /**
   * nodePath => node children
   */
  treeNodeChildrenMap: Record<string, any[]>
  treeNodeChildrenFetching: Record<string, boolean>
  permissionFlag?: boolean,
  resourceMap: any,
  groupByType: boolean // 是否按数据库类型展示，false 默认，true 按数据库类型
  isRefreshingNode: boolean // 是否正在刷新单个节点
  onLoadQueue: string[] // 正在加载的节点队列
}

const initialState: SdtState = {
  treeData: [],
  selectedNode: {} as TreeNode,
  mulSelectedNodes: [],
  rightClickedNode: {},
  expandedKeys: [],
  loadedKeys: [],
  treeLoading: false,
  batchCreateConnectionLog: [],
  treeNodeChildrenMap: {},
  treeNodeChildrenFetching: {},
  resourceMap: {},
  groupByType: false,
  isRefreshingNode: false,
  onLoadQueue: [],
}
const getTargetNode = (nodePath: string, treeData: TreeNode[]) => {
  const queue = [...treeData]
  while (queue.length) {
    const currentNode = queue.shift()
    if (!currentNode) return
    if (currentNode.nodePath === nodePath) {
      return currentNode
    }
    if (currentNode.children) {
      queue.push(...currentNode.children)
    }
  }
}
const isChildKeyOf = (childKey: string, parentKey: string) =>
  childKey !== parentKey && childKey.startsWith(parentKey)

// 更新树节点 children
export const fetchTreeNodeChildren = createAsyncThunk<
  SdtResponse,
  NodeEntity,
  { state: RootState }
>(
  'sdt/fetchTreeNodeChildren',
  async (params) => {
    // 根据isSdtNode判断是不是sdt节点，防止在fetchTreeNodeChildren.fulfilled中将treeData的父节点的children设置为空
    const realParams = { ...params, isSdtNode: undefined }
    const list = await getNodeChildList(realParams);
    console.log(list, 'fetchTreeNodeChildren result')
    return list
  },
  {
    condition({ nodePath, notMemorize = false }, { getState }) {
      const {
        sdt: { treeNodeChildrenFetching, treeNodeChildrenMap, loadedKeys },
      } = getState()

      const fetching = treeNodeChildrenFetching[nodePath];
      //notMemorize = true, 是不做缓存的 每次都需要重新请求
      // if (fetching &&!notMemorize && !treeNodeChildrenMap[nodePath]) {
      //   return true
      // }
    
      // 应用当前已经在获取数据，则不再重复请求
      return !fetching
    },
  },
)

// 直接从 map 中获取节点列表，如果尚未请求过，触发请求
export const getTreeNodeChildren = createAsyncThunk<
ResponseNodeEntity[],
  NodeEntity,
  { state: RootState }
>('sdt/getTreeNodeChildren', async (params, { dispatch, getState }) => {
  const { nodePath} = params
  if (!nodePath) {
    throw new Error('invalid node')
  }
  const {
    sdt: { treeNodeChildrenMap, treeNodeChildrenFetching, groupByType },
    sdtPermission: { status = false }
  } = getState()

  // 如果此节点的子节点已在获取中，则直接返回空数组，防止重复请求导致 ConditionError
  if (treeNodeChildrenFetching[nodePath]) {
    return [];
  }

  const memo = treeNodeChildrenMap[nodePath]

  //load 缓存中有
  if (memo) {
    dispatch(
      setTreeDataNodeChildren({
        nodePath,
        children: memo.map(formatTreeNode),
      }),
    )
    // 使用缓存数据时也需要从加载队列中移除节点
    dispatch(removeFromOnLoadQueue(nodePath))
    return memo
  }
  const isParentLoading = Object.keys(treeNodeChildrenFetching).some(d => treeNodeChildrenFetching[d] && d !== nodePath && nodePath.includes(d))
  if (isParentLoading) {
    const timeout = nodePath.split('/')?.length ?? 0;
    return new Promise((res) => {
      setTimeout(async function () {
        const list = await dispatch(fetchTreeNodeChildren({
          ...params,
          globalHavePermissionFlag: status,
          groupByType
        })).unwrap()
        res(list?.data)
      }, timeout * 200)
    })
  }

  // 字典搜索 需要 查找到父节点，父节点hasChild =false则不进行请求
  const parentNodePath = nodePath && nodePath.match(/.*\//)?.[0].slice(0, -1);
  const findParentNode: any = parentNodePath && treeNodeChildrenMap?.[parentNodePath]?.find(i => i.nodePath === nodePath)

  if (findParentNode?.hasChild === false && !_.isEmpty(findParentNode)) {
    return []
  }
  const list = await dispatch(fetchTreeNodeChildren({
    ...params,
    globalHavePermissionFlag: status,
    groupByType
  })).unwrap()
  return list?.data
})

export const sdtSlice = createSlice({
  name: 'sdt',
  initialState,
  reducers: {
    setTreeData(state, action: PayloadAction<TreeNode[]>) {
      state.treeData = action.payload
    },
    // 重置某节点的子树
    refreshChildTreeCache(state, action: PayloadAction<string>) {
      const nodePath = action.payload
      const { loadedKeys, treeNodeChildrenMap } = state
      // 重置目标节点所有子节点的加载状态
      state.loadedKeys = loadedKeys.filter(
        (key) => !isChildKeyOf(key.toString(), nodePath),
      )
      // 重置目标节点所有子节点的列表缓存
      for (const key of Object.keys(treeNodeChildrenMap)) {
        if (isChildKeyOf(key.toString(), nodePath)) {
          delete treeNodeChildrenMap[key]
        }
      }
    },
    setTreeDataNodeChildren: (
      state,
      action: PayloadAction<{
        nodePath: string
        children: TreeNode[]
      }>,
    ) => {
      const { nodePath, children } = action.payload
      const parent = getTargetNode(nodePath, state.treeData)
      if (parent) {
        parent.children = children
      }
    },
    resetTreeNodeChildrenMap: (state) => {
      state.treeNodeChildrenMap = {}
    },
    setSelectedNode: (state, action: PayloadAction<any>) => {
      state.selectedNode = action.payload
    },
    setMulSelectedNodes: (state, action: PayloadAction<any[]>) => {
      state.mulSelectedNodes = action.payload
    },
    setRightClickedNode: (state, action: PayloadAction<any>) => {
      state.selectedNode = action.payload
      state.rightClickedNode = action.payload
    },
    setExpandedKeys: (state, action: PayloadAction<(string | number)[]>) => {
      state.expandedKeys = action.payload
    },
    setLoadedKeys: (state, action: PayloadAction<(string | number)[]>) => {
      state.loadedKeys = action.payload
    },
    setTreeLoading: (state, action: PayloadAction<boolean>) => {
      state.treeLoading = action.payload
    },
    pushBatchCreateConnectionLog: (
      state,
      action: PayloadAction<IBatchCreateConnectionLog>,
    ) => {
      state.batchCreateConnectionLog.push(action.payload)
    },
    clearBatchCreateConnectionLog: (state) => {
      state.batchCreateConnectionLog = []
    },
    // 设置展示类型
    setGroupByType: (state, action: PayloadAction<boolean>) => {
      state.groupByType = action.payload
    },
    // 清理所有正在进行的请求状态，避免 ConditionError
    clearTreeNodeChildrenFetching: (state) => {
      state.treeNodeChildrenFetching = {}
    },
    // 设置是否正在刷新单个节点
    setIsRefreshingNode: (state, action: PayloadAction<boolean>) => {
      state.isRefreshingNode = action.payload
    },
    // 添加节点到加载队列
    addToOnLoadQueue: (state, action: PayloadAction<string>) => {
      const nodeKey = action.payload
      // 确保 onLoadQueue 已初始化
      if (!state.onLoadQueue) {
        state.onLoadQueue = []
      }
      if (!state.onLoadQueue.includes(nodeKey)) {
        state.onLoadQueue.push(nodeKey)
      }
    },
    // 从加载队列中移除节点
    removeFromOnLoadQueue: (state, action: PayloadAction<string>) => {
      const nodeKey = action.payload
      // 确保 onLoadQueue 已初始化
      if (!state.onLoadQueue) {
        state.onLoadQueue = []
        return
      }
      state.onLoadQueue = state.onLoadQueue.filter(key => key !== nodeKey)
    },
    // 清空加载队列（安全机制使用）
    clearOnLoadQueue: (state) => {
      state.onLoadQueue = []
    },
    // 重置 SDT 到初始状态（安全机制使用）
    resetSdtToInitialState: (state) => {
      // 重置所有关键状态到初始值
      state.expandedKeys = []
      state.loadedKeys = []
      state.onLoadQueue = []
      state.treeNodeChildrenMap = {}
      state.treeNodeChildrenFetching = {}
      state.selectedNode = {} as TreeNode
      state.mulSelectedNodes = []
      state.rightClickedNode = {}
      state.isRefreshingNode = false
      // 保留 treeData 和 groupByType，因为这些可能需要重新获取
      console.log('SDT 状态已重置到初始状态')
    }
  },
  extraReducers: (builder) => {
    builder.addCase(fetchTreeNodeChildren.pending, (state, action) => {
      const { nodePath } = action.meta.arg
      const { treeNodeChildrenFetching } = state
      treeNodeChildrenFetching[nodePath] = true
    })
    builder.addCase(fetchTreeNodeChildren.fulfilled, (state, action) => {
      const list = action.payload
      const { nodePath, nodeType, connectionId, pageSize=0, pageNo=0, notMemorize, isSdtNode = true } = action.meta.arg

      console.log(nodePath, state, 'fullfilled')

      const target: any = getTargetNode(nodePath, state.treeData)
      const isSupportLoadMore = !!pageSize
      //未找到父节点信息时，将此treeNodeChildrenFetching设置为false，允许后续再次请求
      if (!target) {
        console.log('未找到父节点')
        state.loadedKeys = state.loadedKeys.filter((key) => key !== nodePath)
        state.treeNodeChildrenFetching[nodePath] = false
        return 
      }
      if (target && isSdtNode) {
        const listData = list?.data || []
        const result = listData?.map(formatTreeNode)

        // 支持分页逻辑
        if(isSupportLoadMore){
          const children = target?.children || []
          const filterChildren = children?.filter((item: any) => !item?.isLoadMore)
          const loadMoreParams = [{
            ...action.meta.arg,
            isLoadMore: true,
            hasChild: false,
            pageNo: pageNo + 1,
            pageSize,
            notMemorize: true,
            startPageNum: list?.startPageNum || 1
          }]?.map(formatTreeNode)
          // 当前数据不足pageSize时，说明没有更多数据
          const newChildren = listData.length < pageSize ? [...filterChildren, ...result] : [...filterChildren, ...result, ...loadMoreParams]
          // 去重处理，避免重复的 key
          target.children = newChildren.filter((item, index, arr) =>
            arr.findIndex(child => child.key === item.key) === index
          )
        }else{
          // 原有不分页逻辑 - 添加去重处理
          target.children = result.filter((item, index, arr) =>
            arr.findIndex(child => child.key === item.key) === index
          )
        }
      }
      // memoize(分页数据不缓存，其他数据缓存)
      if(!notMemorize){
        state.treeNodeChildrenMap[nodePath] = list?.data
      } 
      state.treeNodeChildrenFetching[nodePath] = false
      // 从加载队列中移除已完成的节点
      state.onLoadQueue = state.onLoadQueue.filter(key => key !== nodePath)
      // 资源纳管 连接层
      if (nodeType === 'connection') {
        state.resourceMap[connectionId] = list?.resourceLimit
      }
    })
    builder.addCase(fetchTreeNodeChildren.rejected, (state, action) => {
      const { nodePath } = action.meta.arg
      state.expandedKeys = state.expandedKeys.filter((key) => key !== nodePath)
      state.loadedKeys = state.loadedKeys.filter((key) => key !== nodePath)
      state.treeNodeChildrenFetching[nodePath] = false
      // 从加载队列中移除失败的节点
      state.onLoadQueue = state.onLoadQueue.filter(key => key !== nodePath)
    })
  },
})

export const sdtReducer = sdtSlice.reducer

export const {
  setTreeData,
  refreshChildTreeCache,
  setTreeDataNodeChildren,
  setSelectedNode,
  setMulSelectedNodes,
  setRightClickedNode,
  setExpandedKeys,
  setLoadedKeys,
  setTreeLoading,
  resetTreeNodeChildrenMap,
  pushBatchCreateConnectionLog,
  clearBatchCreateConnectionLog,
  setGroupByType,
  clearTreeNodeChildrenFetching,
  setIsRefreshingNode,
  addToOnLoadQueue,
  removeFromOnLoadQueue,
  clearOnLoadQueue,
  resetSdtToInitialState,
} = sdtSlice.actions

//调整keys顺序
export function sortExpandedKeys(keys: any[]): string[] {
  return [...keys].sort((a, b) => a.split('/').length - b.split('/').length)
}

export const fetchConnections = (): AppThunk => async (dispatch, getState) => {
  const {
    sdt: { expandedKeys, groupByType },
    sdtPermission: { status }
  } = getState()

  // 清理所有正在进行的请求状态，确保新的展开请求不会被阻止
  dispatch(clearTreeNodeChildrenFetching())
  dispatch(setTreeLoading(true))
  dispatch(setExpandedKeys([]))
  dispatch(getToolbarConnections())
  await getNodeChildList({ nodeType: 'root', nodePath: '/root', globalHavePermissionFlag: status, groupByType })
    .then((list) => {
      let treeData = []
      if(groupByType){
        treeData = formatTreeNodeWithGroupByType(list?.data)
      }
      else {
        treeData = list?.data.map(formatTreeNode)
      }
      dispatch(setTreeData(treeData))
      batch(() => {
        const sortedExpandkeys = sortExpandedKeys([...expandedKeys])
        dispatch(setLoadedKeys([]))
        dispatch(setExpandedKeys(sortedExpandkeys))
        console.log(sortedExpandkeys)
      })
    })
    .finally(() => {
      dispatch(setTreeLoading(false))
    })
}
let refreshOnRootDebounce: ReturnType<typeof _.debounce> | null = null;
export const refreshOnRoot = (): AppThunk => async(dispatch) => {
  // 设置正在刷新状态
  dispatch(setIsRefreshingNode(true))
  // 清理所有正在进行的请求状态，避免 ConditionError
  dispatch(clearTreeNodeChildrenFetching())

  //刷新根节点，将正在加载的节点都清空，重新加载
  await dispatch(setLoadedKeys([]))

  dispatch(resetTreeNodeChildrenMap())

  if (!refreshOnRootDebounce) {
    refreshOnRootDebounce = _.debounce(() => {
      dispatch(fetchConnections())
    }, 500); // Adjust debounce delay as needed
  }

  refreshOnRootDebounce();
}

export const clearRefreshDebounce = () => {
  if (refreshOnRootDebounce) {
    refreshOnRootDebounce.cancel(); // 取消未执行的 debounce
    refreshOnRootDebounce = null;
  }
};

export const viewElementInEditor =
  (selectedNode: Partial<TreeNode>): AppThunk =>
    (dispatch) => {
      const { nodeType, nodePath, nodePathWithType } = selectedNode
      if (!nodeType) return
      // 后端处理视图和物化视图一样，将物化视图的nodeType改为'view'
      let nodeTypeTrans = nodeType
      if (nodeType === 'materializedView') {
        nodeTypeTrans = 'view'
      }
      const { connectionType, connectionId, nodeName } = selectedNode
      const schemaName = getInfoFromPath(nodePath, 'schema', connectionType)
      const nodeParams = {
        connectionId,
        connectionType,
        nodeName,
        nodePath,
        nodePathWithType,
        nodeType: nodeTypeTrans,
      }
      openSdtNode(nodeParams).then(({ generatedSql }) => {
        const databaseName = getInfoFromPath(nodePath, 'database', connectionType)
        let plSql = ['procedure', 'function', 'synonym'].includes(nodeType)
         //StarRocks 查看函数自动执行
        if (connectionType === 'StarRocks' && nodeType === 'function') {
          plSql = false;
        }
        // CQ-4506 OceanBase，右键打开类型，不需要自动执行
        if (connectionType === 'OceanBase' && nodeType === 'type') {
          plSql = true;
        }
        const paneType = plSql
          ? connectionType === 'SQLServer'
            ? 'tSql'
            : 'plSql'
          : 'monaco'
        dispatch(
          addPane({
            tabName: nodeName,
            value: generatedSql,
            connectionId,
            connectionType,
            databaseName: ['DamengDB', 'DB2'].includes(connectionType as any) ? schemaName : databaseName,
            schemaName,
            plSql,
            tSql: paneType === 'tSql',
            autoRun: !plSql,
            paneType: paneType,
            nodeType,
          }),
        )
      })
    }
  // 打开任务
export const openTaskExecutionInEditor =
(selectedNode: Partial<TreeNode>): AppThunk =>
  (dispatch) => {
    const { nodeType, nodePath } = selectedNode
    if (!nodeType) return
    // 后端处理视图和物化视图一样，将物化视图的nodeType改为'view'
    let nodeTypeTrans = nodeType
    // if (['materializedView', 'syncMaterializedView', 'asyncMaterializedView'].includes(nodeType)) {
    //   nodeTypeTrans = 'view'
    // }
    const { connectionType, connectionId, nodeName } = selectedNode
    const schemaName = getInfoFromPath(nodePath, 'schema', connectionType)
    const nodeParams = {
      connectionId,
      connectionType,
      nodeName,
      nodePath,
      nodeType: nodeTypeTrans,
    }
    openTaskExecution(nodeParams).then(({ generatedSql }) => {
      const databaseName = getInfoFromPath(nodePath, 'database', connectionType)
      let plSql = ['procedure', 'function', 'synonym'].includes(nodeType)
      //StarRocks 查看函数自动执行
      if (connectionType === 'StarRocks' && nodeType === 'function') {
        plSql =false;
      }
      const paneType = plSql
        ? connectionType === 'SQLServer'
          ? 'tSql'
          : 'plSql'
        : 'monaco'
      // mariaDB mysql oracle oracleCDB postgres sqlserver 六大数据源打开普通编译器 不分句
      let editorFlag = false
      if (connectionType === 'MySQL' || connectionType === 'OracleCDB' || connectionType === 'Oracle' || connectionType === 'SQLServer' || connectionType === 'PostgreSQL' || connectionType === 'MariaDB') {
        editorFlag = true
      }
      
      dispatch(
        addPane({
          tabName: nodeName,
          value: generatedSql,
          connectionId,
          connectionType,
          databaseName: ['DamengDB', 'DB2'].includes(connectionType as any) ? schemaName : databaseName,
          schemaName,
          plSql,
          tSql: paneType === 'tSql',
          autoRun: !plSql,
          paneType: editorFlag ? 'monaco' : paneType,
        }),
      )
    })
}
// 刷新任务
export const openRefreshTaskInEditor =
(selectedNode: Partial<TreeNode>): AppThunk =>
(dispatch) => {
  const { nodeType, nodePath } = selectedNode
  if (!nodeType) return
  // 后端处理视图和物化视图一样，将物化视图的nodeType改为'view'
  let nodeTypeTrans = nodeType
  // if (['materializedView', 'syncMaterializedView', 'asyncMaterializedView'].includes(nodeType)) {
  //   nodeTypeTrans = 'view'
  // }
  const { connectionType, connectionId, nodeName } = selectedNode
  const schemaName = getInfoFromPath(nodePath, 'schema', connectionType)
  const nodeParams = {
    connectionId,
    connectionType,
    nodeName,
    nodePath,
    nodeType: nodeTypeTrans,
  }
  openRefreshTask(nodeParams).then(({ generatedSql }) => {
    const databaseName = getInfoFromPath(nodePath, 'database', connectionType)
    let plSql = ['procedure', 'function', 'synonym'].includes(nodeType)
    //StarRocks 查看函数自动执行
    if (connectionType === 'StarRocks' && nodeType === 'function') {
      plSql =false;
    }
    const paneType = plSql
      ? connectionType === 'SQLServer'
        ? 'tSql'
        : 'plSql'
      : 'monaco'
    // mariaDB mysql oracle oracleCDB postgres sqlserver 六大数据源打开普通编译器 不分句
    let editorFlag = false
    if (connectionType === 'MySQL' || connectionType === 'OracleCDB' || connectionType === 'Oracle' || connectionType === 'SQLServer' || connectionType === 'PostgreSQL' || connectionType === 'MariaDB') {
      editorFlag = true
    }
    
    dispatch(
      addPane({
        tabName: nodeName,
        value: generatedSql,
        connectionId,
        connectionType,
        databaseName: ['DamengDB', 'DB2'].includes(connectionType as any) ? schemaName : databaseName,
        schemaName,
        plSql,
        tSql: paneType === 'tSql',
        autoRun: !plSql,
        paneType: editorFlag ? 'monaco' : paneType,
      }),
    )
  })
}
/** 调用存储过程 */
export const callProcedure = (selectedNode: Partial<TreeNode>): AppThunk =>
  (dispatch) => {
    const { connectionId, connectionType, nodeName, nodePath, nodeType, nodePathWithType } = selectedNode
    const nodeParams = {
      connectionId,
      connectionType,
      nodeName,
      nodePath,
      nodeType,
      nodePathWithType,
    }
    getCallStatement(nodeParams).then((res) => {
      const databaseName = getInfoFromPath(
        nodePath,
        'database',
        connectionType,
      )
      const generatedSql = res.generatedSql
      const plSql = res.plSql as boolean
      const schemaName = getInfoFromPath(
        nodePath,
        'schema',
        connectionType,
      )
      const paneType = plSql
        ? connectionType === 'SQLServer'
          ? 'tSql'
          : 'plSql'
        : 'monaco'
      dispatch(
        addPane({
          tabName: nodeName,
          value: generatedSql || '',
          connectionId,
          connectionType,
          databaseName,
          schemaName,
          plSql,
          // CQ-4478 函数调用和存储过程调用默认不执行
          // autoRun: !!plSql,
          autoRun: false,
          paneType: paneType,
          menuType: 'call'
        }),
      )
    })
  }
export const showViewInEditor =
  (selectedNode: Partial<TreeNode>): AppThunk =>
    (dispatch) => {
      const { nodeType, nodePath } = selectedNode
      if (!nodeType) return
      const { connectionType, connectionId, nodeName, nodePathWithType } = selectedNode
      const nodeParams = {
        connectionId,
        connectionType,
        nodeName,
        nodePath,
        nodeType,
        nodePathWithType,
      }
      showSdtNodeView(nodeParams).then(({ generatedSql }) => {
        const databaseName = getInfoFromPath(nodePath, 'database')
        const plSql = [
          'trigger',
          'procedure',
          'function',
          'synonym',
          // 'sequence',
          'package',
          'type',
          'packageBody',
        ].includes(nodeType)
        const paneType = plSql
          ? connectionType === 'SQLServer'
            ? 'tSql'
            : 'plSql'
          : 'monaco'
        dispatch(
          addPane({
            tabName: nodeName,
            value: generatedSql,
            connectionId,
            connectionType,
            // databaseName: (['PostgreSQL'] as DataSourceType[]).includes(
            //   connectionType!,
            // )
            //   ? getInfoFromPath(nodePath, 'schema', 'PostgreSQL')
            //   : databaseName,
            databaseName,
            plSql,
            tSql: paneType === 'tSql',
            autoRun: false,
            paneType: paneType,
            schemaName:
              !!connectionType &&
                DataSourceWithAdditionalSchema.includes(connectionType)
                ? getInfoFromPath(nodePath, 'schema', connectionType)
                : undefined,
          }),
        )
      })
    }

/** 合并 plSql 和 tSql 两者编辑器除了icon以外其他相同*/
export const openSqlEditor =
  (openType: PaneType, selectedNode: Partial<TreeNode>): AppThunk =>
    (dispatch) => {
      const { nodePath } = selectedNode
      const { connectionType, connectionId, nodeName } = selectedNode
      const databaseName = getInfoFromPath(nodePath, 'database')
      const isTsql = openType === 'tSql' ? true : false
      dispatch(
        addPane({
          tabName: nodeName,
          connectionId,
          connectionType,
          databaseName: (['PostgreSQL'] as DataSourceType[]).includes(
            connectionType!,
          )
            ? getInfoFromPath(nodePath, 'schema', 'PostgreSQL')
            : databaseName,
          plSql: true,
          tSql: isTsql,
          paneType: openType,
        }),
      )
    }

export const viewRedisKey =
  (selectedNode: Partial<TreeNode>): AppThunk =>
    (dispatch) => {
      const { nodeType, nodePath } = selectedNode
      if (!nodeType) return
      const { connectionType, connectionId, nodeName, nodePathWithType } = selectedNode
      const databaseName = getInfoFromPath(nodePath, 'database')
      dispatch(
        addPane({
          tabName: [databaseName, nodeName].join(' / '),
          connectionType,
          connectionId,
          databaseName,
          paneType: 'grid',
          nodePath,
          nodeName,
          nodeType,
          nodePathWithType
        }),
      )
    }
    
// 切换展示类型
export const changeGroupByType = (value: boolean): AppThunk => (dispatch, getState) => {
  const { sdt: { groupByType: currentGroupByType, isRefreshingNode } } = getState();
  // 只有值变化时且不在刷新状态时触发更新
  if (value !== currentGroupByType && !isRefreshingNode) {
    // 先更新 groupByType
    dispatch(setGroupByType(value));
    dispatch(setExpandedKeys([]))
    dispatch(refreshOnRoot());
  }
};