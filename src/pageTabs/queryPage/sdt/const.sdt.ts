import type { ResponseNodeEntity, TreeNode } from 'src/types'

export const formatTreeNode = (node: any): TreeNode => {
  const { nodeName, nodePath, hasChild, isLoadMore } = node
  return {
    ...node,
    key: isLoadMore ? nodePath + '__loadMore' : nodePath, // 构造loaderMore节点唯一key
    title: nodeName,
    isLeaf: !hasChild,
  }
}

// 按照数据库类型分组提示，根据数据库类型对后端返回的连接节点进行分组
export const formatTreeNodeWithGroupByType = (treeData: any[]): TreeNode[] => {
  const dataSourceTypes = treeData.map(item => item.connectionType)
  const uniqueDataSourceTypes = [...new Set(dataSourceTypes)]
  const newTreeData = uniqueDataSourceTypes.map(dataSourceType => {
    const children = treeData.filter(node => node.connectionType === dataSourceType)?.map(formatTreeNode)
    const childrenCount = children.length
    return {
      nodeType:"datasource",
      key:dataSourceType,
      isLeaf:false,
      title: dataSourceType + '(' + childrenCount + ')',
      children
    }
  })
  return newTreeData as TreeNode[]
}

export const matchKeyword = (target = '', substring = '') => {
  if (!target) return false
  return target.toLowerCase().indexOf(substring.toLowerCase()) > -1
}

/** 搜索节点的返回值转换成NodeTree
 * @param ResponseNodeTree 服务器返回的节点树
 * @return treeNode 转换后的节点树
 */
export function convertResponseTreeToNodeTree(
  ResponseNodeTree: ResponseNodeEntity & { children?: ResponseNodeEntity[] },
) {
  const { children, ...node } = ResponseNodeTree
  node.hasChild = !!children?.length
  const treeNode = formatTreeNode(node)
  if (children && children.length) {
    treeNode.children = children.map(convertResponseTreeToNodeTree)
  }
  return treeNode
}
/**
 * 获取节点以及其子节点expandKeys以展开
 * @param treeNode
 * @returns keys
 */
export function getExpandKeysNodeTreeAllChildren(treeNode: TreeNode) {
  const keys: string[] = []
  const getKeys = (node: TreeNode) => {
    if (!node.isLeaf) keys.push(node.key)
    if (node.children) {
      node.children.forEach((child) => getKeys(child))
    }
  }
  getKeys(treeNode)
  return keys
}

export const getExpandNodeNeedPathKeys = (path: string,needSplice?: boolean) => {
    let result = [];
    const parts = path?.split('/') || [];
    let current = '';
    for (let i = 1; i < parts.length; i++) {
      current += '/' + parts[i];
      result.push(current);
    }
    if (needSplice) {
      result.splice(0, 1); // 删除第一个空字符串
    }
    return result;
}

export const getPreExpandNodeNeedPathKeys = (path: string) => {
  let result = [];
  const parts = path?.split('/') || [];
  let current = '/root';
  for (let i = 2; i < parts.length-1; i++) {
    current += '/' + parts[i];
    result.push(current);
  }
 
  return result;
}
export const getGroupExpandNodeNeedPathKeys = (path: string,pre: string) => {
  let result = [];
  const parts = path?.split('/') || [];
  let current = pre;
  for (let i = 0; i < parts.length; i++) {
    current +=  ((i === parts.length || i === 0) ? '' : '/') + parts[i] ;
    result.push(current);
  }
  return result;
}

// 获取树的所有节点 keys（用于展开）
export function getAllExpandedKeys(treeData: TreeNode[]): string[] {
  const keys: string[] = []

  const traverse = (nodes: TreeNode[]) => {
    nodes.forEach(node => {
      if (!node.isLeaf) {
        keys.push(node.key)
      }
      if (node.children && node.children.length > 0) {
        traverse(node.children)
      }
    })
  }

  traverse(treeData)
  return keys
}

export const getDictNodeParameters = (option: any) => {

  const { nodePathWithType } = option;
  const allExpandKeys = getExpandNodeNeedPathKeys(option?.nodePath, true);
  //nodePat 所有父级
  const shouldExpandKeys = allExpandKeys.slice(0, allExpandKeys.length - 1);

  //父级 nodePath总长度
  const parentNodePathWithTypes = getExpandNodeNeedPathKeys(nodePathWithType);

  //以下层级 nodePathWithTypes 和上个节点保持一致
  const level1Names = ['表', '外表', '视图组', '集合', '物化视图', '键组', '文件组', '函数组', '存储过程组', '任务组', '同义词', '序列组', '触发器组', '数据库连接', '包', '包体', '任务组', '字典组', 'GridFs存储桶', '用户自定义函数', 'flexTableGroup']
  const level2Names = ['列组', '索引组', '约束组', '外键组', '触发器组', '分区组'];
  let asyncActions: {nodePath: string; nodePathWithType: string}[] = [];
  let nodePathWithTypeArr = parentNodePathWithTypes;

  //@ts-ignore
  shouldExpandKeys?.map((key, index) => {
    //是否是表...结尾
    let endType = key.substr(key.lastIndexOf('/') + 1);
    //如果是组 则nodePathWithType = null;
    if (/^\/root\/(g-\d+)(\/g-\d+)*$/.test(key)) {
      //@ts-ignore
      nodePathWithTypeArr.splice(index, 0, null);
    } else if (level1Names.includes(endType) || level2Names.includes(endType)) {
      const preNType = nodePathWithTypeArr[index - 1];
      nodePathWithTypeArr.splice(index, 0, preNType);
    }
  })

  shouldExpandKeys.map((key, index) => asyncActions.push({ nodePath: key, nodePathWithType: nodePathWithTypeArr[index] }))
  return asyncActions;
}