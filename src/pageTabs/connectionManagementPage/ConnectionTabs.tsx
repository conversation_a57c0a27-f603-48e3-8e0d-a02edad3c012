/**
 * 连接tabs
 */
import React, { useEffect, useState } from "react"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON> } from 'antd'
import { useTranslation } from "react-i18next"
import { useRequest, useDispatch, useSelector } from 'src/hook'
import { connectionIsArchive, getdmsConnectionConfig, getHostSSH } from 'src/api'
import ConnectionOverview from './ConnectionOverview'
import ConnnectionManage from './ConnectionManage'
import ConnectionSetting from './ConnectionSetting'
import { ResourceConfig } from './main/resourceConfig/index'
import { setIsArchiveConnection, setRefreshTabConnection } from 'src/pageTabs/connectionManagementPage/connectionManagementPageSlice'
import { pagePermissionCanEdit } from 'src/api'
import { resetGuideSteps } from 'src/pageTabs/SceneGuideModal/guideSlice';
import { Iconfont } from 'src/components'
import classnames from 'classnames'
import styles from './index.module.scss'
import HostConnectionSetting from "./HostConnectionSetting"

 
interface IProps {
  handleShowContentChange: (to: string, from?: string) => void
  [p: string]: any
}
const ConnectionTabs = (props: IProps) => {
  const dispatch = useDispatch()
  const { t } = useTranslation()
  const { refreshTabConnection } = useSelector((state) => state.connectionManagement)

  const {
    connectionId,
    connectionName,
    dataSourceType,
    handleShowContentChange,
    handleEditContent,
    permissionlist,
    isFirstBuildConnection,
    setIsFirstBuildConnection,
    setIsBuildConnection,
  } = props

  const [curTabs, setCurTabs] = useState('1')
  const [canEdit, setCanEdit] = useState<boolean>(true)
  const [visibleGuidePopconfirm, setVisibleGuidePopconfirm] = useState<boolean>(isFirstBuildConnection);

  useEffect(() => {
    setVisibleGuidePopconfirm(isFirstBuildConnection)
  }, [isFirstBuildConnection])

  useEffect(()=>{
    if(connectionId){
      const nodePathWithType = `/CONNECTION:${connectionId}`
      queryPermission(nodePathWithType)
      connectionIsArchiveRun(connectionId)
    }
  },[connectionId])

  // 判断是否为归档连接
  const { data: connectionIsArchiveRes, run: connectionIsArchiveRun } = useRequest(connectionIsArchive,
    {
      manual: true,
      onSuccess(res) {
        dispatch(setIsArchiveConnection(res))
      },
    }
  )

  const { data: hostSSHData } = useRequest(getHostSSH)
  const isDBA = permissionlist?.roleNameList?.find((role: string) => [t("common.text.dba")].includes(role));
  
  // 获取当前模块编辑权限
  const queryPermission = async(nodePathWithType?: string) => {
    const params = {
      systemPermissionType: 'CONNECTION_MANAGEMENT',
      nodePathWithType
    }
    pagePermissionCanEdit(params).then((res: boolean) => {
      setCanEdit(res)
    }).catch((err: any)=>{
      console.error('获取页面权限失败', err)
    });
  }

  useEffect(()=>{
    if(refreshTabConnection){
      refreshConnectionPool()
      dispatch(setRefreshTabConnection(false))
    }
  },[refreshTabConnection])

  // 数据源信息查询
  const {
    data: connectionPool,
    loading: connectionPoolLoading,
    run: queryConnectionConfig,
    refresh: refreshConnectionPool
  } = useRequest(getdmsConnectionConfig, {
    manual: true,
    formatResult: (data: any) => {
      return data
        ?.map((item: any) => {
          if (item?.field === 'password') {
            item.value = item?.value?atob(item?.value):'';
          }
          return item
        })
        ?.map((item: any) => {
          //展示label映射
          let realValue = item?.value;
          
          if (item?.field !== 'connectionPort' && item?.field !== 'isAdminWithAllPerms') {
            realValue  = item?.options
            ? item?.options?.find((e: any) => e?.key === item?.value)?.title
            : item?.value;
          }
          return ({
            ...item,
            label: item?.label,
            value: realValue,
          })
        })
        ?.filter((i: any) => i?.label)
    },
  })

  useEffect(() => {
    if (connectionId) {
      queryConnectionConfig(connectionId)
    }
  }, [connectionId, queryConnectionConfig])

  useEffect(() => {

    if (connectionIsArchiveRes && curTabs !== '1') {
      setCurTabs("1")
    }
  }, [connectionIsArchiveRes, curTabs])

  const handleTabsChange = (key: string) => {
    setCurTabs(key)
  }

  const version = connectionPool?.filter(
    (i: any) => i?.field === 'dataSourceVersion',
  )?.[0]?.value
  const remark = connectionPool?.filter((i: any) => i?.field === 'remark')?.[0]
    ?.value

  const tabTitle = (str: string) => {
    return <Tooltip title={connectionIsArchiveRes? t('db.connection.tabs.archive.tip') : ''}>{str}</Tooltip>
  }

  return (
    <>
    {
      visibleGuidePopconfirm && <div className={styles.mask}></div>
    }
    <div className={styles.connectionTabsWrap}>
      <div className={styles.topLine}>
        <Iconfont
          type={`icon-${dataSourceType}`}
          className={classnames(styles.mr10, styles.titleIcon)}
        />
        <div className={styles.info}>
          <div className={styles.parentName}>
            {dataSourceType}
            {version && <span className={styles.version}>{version}</span>}
            {connectionIsArchiveRes && <span className={styles.isArchiveResMark}>{t('db.connection.tabs.archive')}</span>}
          </div>
          <div className={styles.selfName}>
            {connectionName}
            <Divider type="vertical" />
            <span className={styles.remark}>{remark}</span>
          </div>
        </div>
      </div>
      <Tabs onChange={handleTabsChange} activeKey={curTabs} className={styles.connectionTabs}>
        <Tabs.TabPane tab={t('db.connection.tabs.overview')} key="1" />
        <Tabs.TabPane tab={
          visibleGuidePopconfirm? 
          <Popconfirm
            title={t('db.connection.tabs.overview.guide.content1')}
            onConfirm={() => {
              setVisibleGuidePopconfirm(false);
              setIsFirstBuildConnection(false);
              setIsBuildConnection(false);
              dispatch(resetGuideSteps());
            }}
            onCancel={(event: any) => {
              setVisibleGuidePopconfirm(false); 
              setIsFirstBuildConnection(false);
              setIsBuildConnection(false);
              event.stopPropagation()
              dispatch(resetGuideSteps());
            }}
            okText={t('db.connection.tabs.overview.guide.setting')}
            cancelText={t('common.downloadTemplate.ignore')}
            visible={visibleGuidePopconfirm}
            icon={null}
            overlayClassName={styles.guidePopconfirm}
            cancelButtonProps={{
              style: {
                backgroundColor: '#AAAAAA',
                color: 'white'
              }
            }}
          >
            <span
              className={styles.zIndex999}
            >{t('db.connection.tabs.overview.resourceManagement')}</span>
          </Popconfirm>
          : tabTitle(t('db.connection.tabs.overview.resourceManagement'))
        } key="2" disabled={connectionIsArchiveRes}/>
        <Tabs.TabPane tab={tabTitle(t('db.connection.tabs.overview.poolManagement'))} key="3" disabled={connectionIsArchiveRes}/>       
        <Tabs.TabPane tab={tabTitle(t('db.connection.conn.panel2'))} key="4" disabled={connectionIsArchiveRes}/>
        {
          // dba 角色 && enable 才显示 数据库主机连接tab
          isDBA && hostSSHData?.enable &&
          <Tabs.TabPane tab={tabTitle(t('db.conn.host'))} key="5" disabled={connectionIsArchiveRes}/>
        }
      </Tabs>

      {/* 连接概览详情 */}
      {curTabs === "1" && (
        <ConnectionOverview
          connectionPool={connectionPool}
          connectionPoolLoading={connectionPoolLoading}
          connectionId={connectionId}
          connectionName={connectionName}
          dataSourceType={dataSourceType}
          setCurTabs={setCurTabs}
          handleEditContent={handleEditContent}
          handleShowContentChange={handleShowContentChange}
          permissionlist={permissionlist}
          canEdit={canEdit}
          isArchiveConnection={connectionIsArchiveRes}
        />
      )}

      {/* 资源管理 */}
      {curTabs === "2" && (
        <ResourceConfig
          connectionId={connectionId}
          connectionType={dataSourceType}
          permissionlist={permissionlist}
          canEdit={canEdit}
        />
      )}

      {/* 连接池管理 */}
      {curTabs === "3" && (
        <ConnnectionManage
          connectionId={connectionId}
          dataSourceType={dataSourceType}
          permissionlist={permissionlist}
          canEdit={canEdit}
        />
      )}

      {/* 连接设置 */}
      {curTabs === "4" && 
        <ConnectionSetting
          dataSourceType={dataSourceType}
          connectionId={connectionId}
          permissionlist={permissionlist}
          canEdit={canEdit}
          connectionPool={connectionPool}
        />
      }
      {/* 数据库主机连接 */}
      {curTabs === "5" && 
        <HostConnectionSetting
          dataSourceType={dataSourceType}
          connectionId={connectionId}
          permissionlist={permissionlist}
          canEdit={canEdit}
          hostSSHData={hostSSHData}
          connectionPool={connectionPool}
        />
      }
    </div>
    </>
  );  
}
export default ConnectionTabs