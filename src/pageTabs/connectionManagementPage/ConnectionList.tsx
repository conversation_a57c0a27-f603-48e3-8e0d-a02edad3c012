/**
 * 连接列表
 */
import React, { useEffect, useRef, useMemo, useState, forwardRef, useImperativeHandle, useCallback } from 'react'
import { DownOutlined, EditOutlined } from '@ant-design/icons'
import { useLocation, useHistory } from 'react-router-dom';
import { useTranslation } from 'react-i18next';
import { Input, message, Modal, Menu, Dropdown, Button, Tooltip, Badge, Tabs, Divider } from 'antd'
import { Iconfont, ResizeTableSome } from 'src/components'
import {
  modifyConnectionGroupName,
  testConnection as testConnectionFuc,
  deleteConnectionBatch,
  archiveConnection,
  unarchiveConnection,
  removeFailed,
} from 'src/api'
import { renderTableFields, getScrollX } from 'src/util'
import styles from './index.module.scss'
import classnames from 'classnames'
import { GloablSearchLocationState } from 'src/pageTabs/GlobalSearchModal/WorkOrderResult';
import { ITestConnectionProps, setConnectionCurState, setIsArchiveConnection } from './connectionManagementPageSlice'
import { useDispatch, useSelector } from 'src/hook'
import { debounce } from 'lodash'
import { ConnectionFailWarnImg } from 'src/components/ConnectionFailWarnImg'
import { 
  setRuleManageState,
  setRuleManageDetailParams 
} from 'src/pageTabs/ruleManagePage/ruleManageSlice'

const updateTableData = (data: any[], keys?: any[], testConnection?: ITestConnectionProps[]) => {
  // 对数据list里面的update更新，这样组件监听到update值变化就会去请求然后更新当前连接的状态
  // keys存在，说明触发了批量操作或者单次测试，用拿到的keys去更新list里面update的值
  if (Array.isArray(keys) && keys.length > 0) {
    let newKeys = keys?.map(str => Number(str))
    return data?.map(item => {
      const { connectionId, update } = item || {};
      return newKeys.includes(connectionId) ? { ...item, update: !update } : { ...item };
    })
  }
  else if (Array.isArray(testConnection) && testConnection.length) {
    return data?.map(item => {
      const { connectionId } = item || {};
      const { connectionId: cid, result } = testConnection.find(item => item.connectionId === connectionId) || {}
      return connectionId === cid ? { ...item, alreadyTestedRusult: result } : { ...item };
    })
  } else {
    return data;
  }
}

interface IProps {
  [p: string]: any
}

const ConnectionList = forwardRef((props: IProps, ref) => {
  const {
    connectionData = {},
    loading,
    curTab = "group",
    selectNodeType,
    dataSourceType,
    handleEditContent,
    handleCopyContent,
    handleShowContentChange,
    handleRefreshTree,
    handleRefresh,
    selectedNodeInfo,
    permissionlist,
    handleDeleteConnection,
    setPagination,
    setConnectListSearchValue,
    connectListSearchValue,
  } = props;

  const history = useHistory();
  const dispatch = useDispatch();
  const { t } = useTranslation();
  const location = useLocation<any>()
  const { state = {} } = location as {state: GloablSearchLocationState};
  const { testConnection, connectionCurState } = useSelector((state) => state.connectionManagement)
  const { userInfo } = useSelector((state) => state.login)
  const allFailedCountConnectionIds = useSelector((state) => state.login.allFailedCountConnectionIds);

  let connectionList: any[] = [];

  if (curTab === 'group') {
    if (selectNodeType === "datasource") {
      connectionList = connectionData?.data ?? [];
    } else if (selectNodeType === "group") {
      connectionList = connectionData?.connectionList ?? [];
    }
  } else {
    connectionList = connectionData;
  }

  const { group = {}, pageNo = 1, pageSize = 10, totalItem = 0 } = connectionData
  const { isOnlyRead, roleNameList = [] } = permissionlist || {}
  const inputRef = useRef(null)
  const searchRef = useRef<any>(null)
  const [isEdit, setIsEdit] = useState(false)
  const [groupName, setGroupName] = useState<any>()
  const [connectionListData, setConnectionListData] = useState<any>()
  const [selectedRowKeys, setSelectedRowKeys] = useState<React.Key[]>([]);
  const [selectedRowInfos, setSelectedRowInfos] = useState<any[]>([]);
  const [loadingIds, setLoaingIds] = useState<any[]>([])
  const [tabsKey, setTabsKey] = useState<string>('allConnectionsTab')

  const filterData = useCallback((data: any) => {
    if (!connectListSearchValue) return data
    return data?.filter((item: any) => {
      const { groupName, connectionName, ruleTemplateName, manager, creator, createDateTime, userInputs } = item || {};
      const { serviceName, userName, connectionPort, connectionUrl } = userInputs || {};
      const anyContainSearchString = [
        groupName,
        connectionName,
        ruleTemplateName,
        manager,
        creator,
        createDateTime,
        serviceName,
        userName,
        connectionPort,
        connectionUrl
      ].some(variable => variable?.includes(connectListSearchValue));
      if (anyContainSearchString) return item;
    })
  }, [connectListSearchValue])

  useEffect(() => {
    dispatch(setIsArchiveConnection(false))
  }, [dispatch])

  useEffect(() => {
      setTabsKey(connectionCurState);
  }, [connectionCurState])

  useEffect(() => {
    if (Array.isArray(connectionList)) {
      if (selectNodeType === "group" && curTab !== 'group') {
        setConnectionListData(updateTableData(filterData(connectionList), [], testConnection));
      } else {
        setConnectionListData(updateTableData(connectionList, [], testConnection));
      }
      setSelectedRowKeys([]);
    }
  }, [JSON.stringify(connectionList), filterData, selectNodeType, testConnection])

  useEffect(() => {
    setGroupName(group?.groupName)
  }, [group])

  useEffect(() => {
    if (Array.isArray(connectionListData)) {
      setConnectionListData(updateTableData(connectionListData, [], testConnection));
    }
  }, [testConnection])

  useImperativeHandle(ref, () => ({
    handleTestConnection
  }));
  
  useEffect(() => { //选中的节点变化，要清空模糊搜索框
    searchRef?.current?.setValue(undefined)
  }, [JSON.stringify(selectedNodeInfo), curTab])

  useEffect(() => {
    setSelectedRowKeys([])
  }, [tabsKey])

  useEffect(() => {
    if (state?.globalSearchRecordPosition) {
      //处理分页 并选中
      const pageNum = Math.ceil(Number(state.globalSearchRecordPosition) / 10);
     if (pageNum !== pageNo) {
      setPagination({
        pageNo: pageNum, 
        pageSize
      })
     }
    }
  }, [state?.globalSearchRecordPosition, pageNo])

  // 是DBA或自定义角色授权的模块有权限
  const hasMoreMenuPermission = useMemo(() => {
    const isDBAorSenior =
      !permissionlist?.roleNameList?.some((role: string) => [t('common.text.dba')].includes(role)) &&
      !permissionlist?.roleTypeList?.some((item: string) => item.startsWith('CUSTOM_SYSTEM'));

    return !isDBAorSenior && !permissionlist?.isOnlyRead
  }, [permissionlist?.isOnlyRead])

  const handleEditName = () => {
    setIsEdit(true)
    // @ts-ignore
    inputRef?.current && inputRef?.current?.focus()
  }

  const handleNameChange = (e: any) => {
    const value = e.target.value?.trim();
    setGroupName(value);
  }

  // 修改组名
  const handleModifyName = () => {
    if (!groupName || (groupName === group?.groupName)) {
      setGroupName(group?.groupName);
      setIsEdit(false);
      return
    }
    const params = {
      id: group?.id,
      groupName,
    };
    modifyConnectionGroupName(params)
      .then(() => {
        message.success(t('common.message.editSuccess'));
        handleRefreshTree();
      })
      .catch((err: any) => {
        setGroupName(group?.groupName);
        console.error("修改失败", err);
      });
    setIsEdit(false);
  };

  // 模糊搜索防抖
  const onSearch = debounce((e) => {
    setConnectListSearchValue(e?.target?.value)
  }, 500)

  // input onchange
  const handleInputChange = (e: any) => {
    const currentEvent = e.nativeEvent
    onSearch(currentEvent)
  }

  const handleTestConnectionRecord = (record: any) => {
    const {
      connectionId,
      userInputs = {},
    } = record
    const params: any = {
      connectionId,
      dataSourceType: userInputs?.dataSourceType,
      userInputs,
    };
    return params;
  }

  /**
   * 测试连接
   */
  const handleTestConnection = (params: any) => {
    setLoaingIds((ids: any[]) => {
      return [...ids, params?.connectionId]
    })
    testConnectionFuc(params).then(() => {
      message.success(t('common.message.operateSuccessfully'));
    }).catch((error) => {
      console.error('测试连接 Error occurred:', error);
    }).finally(() => {
      handleRefresh();
      setLoaingIds([]);
    })
  }

  /**
   * 归档连接
   */
  const handleArchiveConnection = (params: number[]) => {
    Modal.confirm({
      centered: true,
      content: t('db.connection.archiveConnection.tip'),
      okText: t('common.btn.confirm'),
      cancelText: t('common.btn.cancel'),
      onOk: () => {
        archiveConnection(params).then(() => {
          message.success(t('db.connection.archiveConnection.success'));
        }).catch((error) => {
          console.error('归档 Error occurred:', error);
        }).finally(() => {
          handleRefresh();
        })
      }
    })
  }

  /**
   * 撤销归档
   */
  const handleUnarchiveConnection = (params: number[]) => {
    Modal.confirm({
      centered: true,
      content: t('db.connection.unfile.tip'),
      okText: t('common.btn.confirm'),
      cancelText: t('common.btn.cancel'),
      onOk: () => {
        unarchiveConnection(params).then(() => {
          message.success(t('db.connection.unfile.success'));
        }).catch((error) => {
          console.error('撤销归档 Error occurred:', error);
        }).finally(() => {
          handleRefresh();
        })
      }
    })
  }

  /**
   * 撤销问题
   */
  const handleRemoveFailed = (params: number[]) => {
    removeFailed(params).then(() => {
      message.success(t('db.connection.undoProblem.success'));
    }).catch((error) => {
      console.error('撤销问题 Error occurred:', error);
    }).finally(() => {
      handleRefresh();
    })
  }

  /**
   * 批量 测试连接
   */
  const testConnections = async () => {
    Promise.all([setLoaingIds(selectedRowKeys)]).then(async () => {
      // 为减轻后端执行压力，每个测试执行结束后，再进行下一个
      for (const items of selectedRowInfos) {
        const params = handleTestConnectionRecord(items);
        try {
          await testConnectionFuc(params);
          message.success(t('common.message.operateSuccessfully'));
        } catch (error) {
          console.error('批量测试 Error occurred:', error);
        } finally {
          // 为明确 接口返回的报错信息与记录的对应关系, 测试一条refresh一次
          handleRefresh();
          setLoaingIds((ids: any[]) => {
            if (!ids) return [];
            return ids?.map((id) => {
              if (params?.connectionId !== id) {
                return id;
              }
              return undefined;
            })
          })
        }
      }
    }).finally(() => {
      setLoaingIds([]);
    })
  }


  const handleDeleteConnections = () => {
    const params: any = {
      connectionIds: selectedRowKeys,
      connectionType: connectionListData?.[0]?.connectionType,
      nodePath: '',
      nodeType: 'connection',
    }
    Modal.confirm({
      title: t('db.connection.batchDelete.tip'),
      okText: t('common.btn.confirm'),
      cancelText: t('common.btn.cancel'),
      onOk: () => {
        deleteConnectionBatch(params).then(() => {
          message.success(t('db.connection.batchDelete.success'));
          setSelectedRowKeys([])
        }).catch((err) => {
          console.error('批量删除连接 error :>> ', err);
        }).finally(() => {
          handleRefresh()
        })
      },
    })
  }

  const onSelectChange = (newSelectedRowKeys: React.Key[], info: any) => {
    setSelectedRowKeys(newSelectedRowKeys);
    setSelectedRowInfos(info);
  };

  const tabsHandleChange = (key: string) => {
    dispatch(setConnectionCurState(key))
    setTabsKey(key)
  };

  const menu = () => {
    const tooltipTitle = t('db.connection.noPerm', {roleNameList: roleNameList.join(", ")});

    const renderTooltip = (children: any) => {
      return (
        <Tooltip title={isOnlyRead ? tooltipTitle : null} style={{ width: 180, display: 'flex', textAlign: 'center' }}>
          {children}
        </Tooltip>
      )
    }

    const menuItems = {
      'allConnectionsTab': [
        <Menu.Item key="batchTest" onClick={testConnections}>{t('db.connection.batchTest')}</Menu.Item>,
        <Menu.Item key="batchArchiveOne" onClick={() => handleArchiveConnection(selectedRowKeys as number[])} disabled={isOnlyRead}>{renderTooltip(t('db.connection.batchArchive'))}</Menu.Item>,
      ],
      'problemConnectionsTab': [
        <Menu.Item key="batchArchiveTwo" onClick={() => handleArchiveConnection(selectedRowKeys as number[])} disabled={isOnlyRead}>{renderTooltip(t('db.connection.batchArchive'))}</Menu.Item>,
        <Menu.Item key="batchRemoveFailed" onClick={() => handleRemoveFailed(selectedRowKeys as number[])} disabled={isOnlyRead}>{renderTooltip(t('db.connection.batchUndoProblem'))}</Menu.Item>,
      ],
      'archiveConnectionTab': [
        <Menu.Item key="batchUnarchive" onClick={() => handleUnarchiveConnection(selectedRowKeys as number[])} disabled={isOnlyRead}>{renderTooltip(t('db.connection.batchUnfile'))}</Menu.Item>,
        <Menu.Item key="batchDel" onClick={handleDeleteConnections} disabled={isOnlyRead}>{renderTooltip(t('common.btn.batchDelete'))}</Menu.Item>,
      ]
    }
    return (
      <Menu>
        {menuItems[tabsKey as keyof typeof menuItems] || <></>}
      </Menu>
    )
  }

  const rowSelection = {
    selectedRowKeys,
    getCheckboxProps: (record: any) => ({
      disabled: !(hasMoreMenuPermission || record?.userId === userInfo?.userId)
    }),
    onChange: onSelectChange,
  };

  const connectionOptions = {
    'allConnectionsTab': [t('common.btn.edit'), t('common.btn.test'), t('common.btn.duplication'), t('common.btn.archive')],
    'problemConnectionsTab': [t('db.connection.undoProblem'), t('common.btn.archive')],
    'archiveConnectionTab': [t('common.btn.duplication'), t('common.btn.delete'), t('db.connection.unfile')],
  }
  
  const getOptionFuc = (record: any): any => {
    const roleStr = roleNameList?.join(", ");
    
    const hasPermission = () => hasMoreMenuPermission || record?.userId === userInfo?.userId;
  
    const createOption = (title: string, onClick: () => void, disabledCondition: boolean | null) => ({
      permissionTooltipTitle: title,
      onClick,
      disabledCondition
    });

    const connectionIds: number[] = [Number(record?.connectionId)];
  
    return {
      [t('common.btn.edit')]: createOption(t('db.connection.noPerm', {roleNameList:roleStr }),
        () => handleEditContent(record),
        !hasPermission()
      ),
      [t('common.btn.test')]: createOption(t('db.connection.noPerm', {roleNameList:roleStr }),
        () => handleTestConnection(handleTestConnectionRecord(record)),
        null
      ),
      [t('common.btn.duplication')]: createOption(t('db.connection.noPerm', {roleNameList:roleStr }),
        () => handleCopyContent(record),
        !hasPermission()
      ),
      [t('common.btn.archive')]: createOption(t('db.connection.noPerm', {roleNameList:roleStr }),
        () => handleArchiveConnection(connectionIds),
        !hasPermission()
      ),
      [t('db.connection.undoProblem')]: createOption(t('db.connection.noPerm', {roleNameList:roleStr }),
        () => handleRemoveFailed(connectionIds),
        !hasPermission()
      ),
      [t('db.connection.unfile')]: createOption(t('db.connection.noPerm', {roleNameList:roleStr }),
        () => handleUnarchiveConnection(connectionIds),
        !hasPermission()
      ),
      [t('common.btn.delete')]: createOption(t('db.connection.noPerm', {roleNameList:roleStr }),
        () => handleDeleteConnection(record),
        !hasPermission()
      )
    }
  }
  

  const renderOptionFuc = (record: any) => {
    return connectionOptions?.[tabsKey as keyof typeof connectionOptions]?.map((item: string) => {
      const options = getOptionFuc(record);
      if (options?.hasOwnProperty(item)) {
        const {
          permissionTooltipTitle,
          onClick,
          disabledCondition
        } = options[item as keyof typeof options];
        return (
          <Tooltip key={record.connectionId + item} title={disabledCondition ? permissionTooltipTitle : ''}>
            <Button
              type='link'
              className={classnames(styles.ml10, item === t('common.btn.delete') ? styles.colorRed : '')}
              onClick={onClick}
              disabled={disabledCondition || false}
            >
              {item}
            </Button>
          </Tooltip>
        )
      }
      return <></>
    });
  }

  // 渲染规则管理列表-查看
  const handleToRuleManagementView = (templateId: string) => {
    history.push('/rule_management')
    dispatch(setRuleManageState('view')) 
    dispatch(setRuleManageDetailParams({ templateId }))
  }

  const columns: any[] = [
    {
      title: t('db.connection.approver.connectionName'),
      dataIndex: 'connectionName',
      key: 'connectionName',
      width: 200,
      ellipsis: true,
      render: (txt: string, record: any) => {
        const { status } = record || {};
        return <div className={styles.connectionNameWrap}>
          <Badge status={loadingIds.includes(record?.connectionId) ? 'processing' : status === "success" ? "success" : "error"} />
          {
            txt ?
              <span
                className={classnames(styles.options, styles.connectionNameText)}
                onClick={() => {
                  handleShowContentChange('tabs', record)
                }}
              >
                {txt?.length > 20 ? <Tooltip placement='topLeft' title={txt}>{txt}</Tooltip> : txt}
              </span> : '-'
          }
          {
            tabsKey === 'problemConnectionsTab' ? <ConnectionFailWarnImg />
              : allFailedCountConnectionIds?.includes(Number(record?.connectionId)) && 
              <ConnectionFailWarnImg />
          }
        </div>
      }
    },
    {
      title: t('db.connection.approver.connectionUrl'),
      dataIndex: 'connectionUrl',
      key: 'connectionUrl',
      width: 350,
      render: (_: string, record: any) => {
        const { connectionUrl, connectionPort, connectionMembers } = record?.userInputs || {}
        return connectionMembers?.length
          ? <Tooltip title={connectionMembers?.map((i: any) => <div>{i?.connectionUrl + ':' + i?.connectionPort}</div>)}>
            {
              connectionMembers?.length > 1 ?
                `${connectionMembers?.[0]?.connectionUrl} :${connectionMembers?.[0]?.connectionPort} ...` :
                `${connectionMembers?.[0]?.connectionUrl} : ${connectionMembers?.[0]?.connectionPort}`
            }
          </Tooltip>
          : connectionUrl + ':' + connectionPort

      }
    },
    {
      title: t('db.connection.approver.ruleTemplateName'),
      dataIndex: 'ruleTemplateName',
      key: 'ruleTemplateName',
      width: 200,
      ellipsis: true,
      render: (value: boolean | React.ReactChild | React.ReactFragment | React.ReactPortal | null | undefined, record: { ruleTemplateId: number }) => {
        const { ruleTemplateId } = record;
        return <>
          {
            ruleTemplateId ?
              <span className='options' onClick={() => handleToRuleManagementView(ruleTemplateId?.toString())}>{value}</span>
              : '-'
          }
        </>
      }
    },
    {
      title: t('db.connection.approver.SID'),
      dataIndex: 'SID',
      key: 'SID',
      width: 200,
      render: (_: string, record: any) => (
        <span>{renderTableFields(record?.userInputs?.serviceName)}</span>
      ),
    },
    {
      title: t('db.connection.conn.userName'),
      dataIndex: 'userName',
      key: 'userName',
      width: 200,
      ellipsis: true,
      render: (_: string, record: any) => (
        <span>{renderTableFields(record?.userInputs?.userName)}</span>
      ),
    },
    {
      title: t('db.connection.conn.approver.connManager'),
      dataIndex: 'manager',
      key: 'manager',
      width: 200,
      ellipsis: true,
      render: (txt: string) => <span>{renderTableFields(txt)}</span>
    },
    {
      title: t('db.connection.approver.creator'),
      dataIndex: 'creator',
      key: 'creator',
      width: 200,
      ellipsis: true,
      render: (txt: string) => <span>{renderTableFields(txt)}</span>
    },
    {
      title: t('db.connection.approver.createDateTime'),
      dataIndex: 'createDateTime',
      key: 'createDateTime',
      width: 200,
      ellipsis: true,
      render: (txt: string) => <span>{txt || '-'}</span>
    },
    {
      title: t('common.text.action'),
      dataIndex: 'option',
      key: 'option',
      fixed: 'right',
      width: 200,
      render: (_: string, record: any) => (
        <div className={styles.options}>
          {renderOptionFuc(record)}
        </div>
      ),
    },
  ]

  // 区分第一项是分组名还是实例名
  if (curTab === 'group') {
    columns.splice(1, 0, {
      title: t('db.connection.group.groupName'),
      dataIndex: 'groupName',
      key: 'groupName',
      width: 200,
      ellipsis: true,
      render: (txt: string) => <span>{renderTableFields(txt)}</span>
    })
  } else if (curTab === 'instance') {
    columns.splice(1, 0, {
      title: t('db.connection.groupName'),
      dataIndex: "groupName",
      key: "groupName",
      width: 200,
      ellipsis: true,
      render: (_: string, record: any) => (
        <span> {record?.hostAddr + ":" + record?.port}</span>
      ),
    });
  }

  const hasSelected = !(Array.isArray(selectedRowKeys) && selectedRowKeys.length > 0);
  const hasGroupInfo = ['datasource', 'group'].includes(selectNodeType)
  const instanceUrl =
    connectionList?.[0]?.userInputs?.connectionUrl +
    ':' +
    connectionList?.[0]?.userInputs?.connectionPort
  const serviceName = connectionList?.[0]?.userInputs?.serviceName

  const isSelectedRowIndex = useMemo(() => {
   
    if (!state?.globalSearchRecordPosition || !pageSize || !pageNo || !state.globalSearchSubTabKey || !state.globalSearchTabKey || !state?.object?.dataSourceType) {
      return null;
    }
    const pageNum = Math.ceil(Number(state.globalSearchRecordPosition) / pageSize);
    if (pageNum === pageNo && tabsKey === state.globalSearchSubTabKey && state.object.dataSourceType === dataSourceType) {
      const itemIndexInPage = state.globalSearchRecordPosition % pageSize;
      return itemIndexInPage === 0 ? pageSize - 1 : itemIndexInPage - 1;
    }
  },[pageNo, pageSize, state?.globalSearchRecordPosition, state?.globalSearchSubTabKey, tabsKey, dataSourceType])

  return (
    <>
      {hasGroupInfo && (
        <div className={styles.headerLine}>
          {selectNodeType === "datasource" ? (
            <div className={styles.namePart}>
              <Iconfont
                type={`icon-connection-${dataSourceType}`}
                className={styles.mr10}
              />
              {dataSourceType}
            </div>
          ) : (
            <div>
              <div className={styles.namePart}>
                <Iconfont
                  type="icon-shujukuwenjianjia"
                  className={styles.mr10}
                />
                {curTab === "group" ? (
                  <>
                    <Input
                      ref={inputRef}
                      style={{
                        width: isEdit ? 200 : 0,
                        padding: isEdit ? "4px 11px" : 0,
                        opacity: isEdit ? 1 : 0,
                      }}
                      value={groupName}
                      onChange={handleNameChange}
                      onBlur={handleModifyName}
                    />
                    <span style={{ opacity: isEdit ? 0 : 1 }}>
                      {groupName}
                      <EditOutlined
                        className={classnames(styles.ml10, styles.options)}
                        onClick={handleEditName}
                      />
                    </span>
                  </>
                ) : (
                  selectedNodeInfo?.nodeName ?? instanceUrl
                )}
              </div>
              {curTab === "group" ? (
                <div className={styles.desc}>{t('db.connection.createUser')}{group?.createUser}</div>
              ) : (
                <div>
                  <div className={styles.desc}>{t('db.connection.nodeName')}{selectedNodeInfo?.nodeName || instanceUrl}</div>
                  <div className={styles.desc}>{t('db.connection.serviceName')}{serviceName ?? ''}</div>
                </div>
              )}
            </div>
          )}
          <div>
            <Input
              allowClear
              placeholder={t('db.connection.search')}
              style={{ width: 240, marginRight: '8px' }}
              onChange={handleInputChange}
              ref={searchRef}
            />
            <Dropdown disabled={hasSelected} overlay={menu}>
              <Button type="primary">{t('common.btn.batchText')}<DownOutlined /></Button>
            </Dropdown>
          </div>

        </div>
      )}
      <Divider />
      <Tabs
        defaultActiveKey='allConnectionsTab'
        type='card'
        activeKey={tabsKey}
        onChange={tabsHandleChange}
        className={styles.contentTabs}
      >
        <Tabs.TabPane tab={t('db.connection.allConnectionsTab')} key='allConnectionsTab' />
        <Tabs.TabPane tab={t('db.connection.problemConnectionsTab')} key='problemConnectionsTab' />
        <Tabs.TabPane tab={t('db.connection.archiveConnectionTab')} key='archiveConnectionTab' />
      </Tabs>
      {/* 连接列表 */}
      <ResizeTableSome
        rowSelection={rowSelection}
        loading={loading}
        columns={columns}
        rowClassName={(record, index) => index === isSelectedRowIndex ? 'globalSearchRowSelected': ''}
        rowKey={(record: any) => record?.connectionId}
        dataSource={connectionListData}
        scroll={{ x: getScrollX(columns), y: "calc(100vh - 440px)" }}
        pagination={
          ['datasource', 'group'].includes(selectNodeType) && curTab === 'group' ?
            {
              current: pageNo,
              pageSize: pageSize,
              size: 'default',
              showSizeChanger: true,
              showQuickJumper: true,
              total: totalItem || 0,
              showTotal: () => t('common.table.pagination.total', {total: totalItem}),
              onChange: (pageNumber, pageSize) => {
                setPagination({ pageNo: pageNumber, pageSize })
              }
            }
            : false
        }
        className={styles.connnectionList}
        autoWidthField={
          [
            "connectionName",
            "connectionUrl",
            "ruleTemplateName",
            "SID", "userName",
            "manager",
            "creator",
            "createDateTime",
            "groupName"
          ]
        }
      />
    </>
  );
})

export default ConnectionList