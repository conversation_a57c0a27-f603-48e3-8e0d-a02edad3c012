import debounce from "lodash/debounce"

interface DownloadOptions {
  href: string
  download?: string
}
export const handleDownload = debounce(({
  href,
  download = 'download',
}: DownloadOptions) => {
  const link = document.createElement('a')
  link.href = href
  link.download = download
  link.style.display = 'none'
  document.body.appendChild(link)
  link.click()
  link.remove()
}, 300)

export const handleDownloadCSV = debounce(async ({
  data,
  fileName
}: any) => {
  //新的方式下载csv文件
  //创建Blob对象，是一种用于存储二进制数据的对象
  let BOM = '\uFEFF'
  const blob = new Blob([BOM + data], { type: 'text/csv' })
  //这段代码用于创建一个临时的URL，该URL指向Blob对象。
  const url = window.URL.createObjectURL(blob)
  //创建一个链接到Blob对象的<a>元素
  const link = document.createElement('a')
  //href属性用于设置链接的目标地址，download属性用于设置链接的下载名称
  link.href = url
  link.setAttribute('download', fileName)
  document.body.appendChild(link)
  link.click()
  window.URL.revokeObjectURL(url)
  document.body.removeChild(link)
}, 300)